export interface CreatePaymentResponse {
  acceptablePaymentType?: string;
  amount?: number;
  amountOutstanding?: number;
  amountPaid?: number;
  /**
   * authKey
   */
  authKey?: string;
  billerCode?: string;
  crn1?: string;
  crn2?: string;
  crn3?: string;
  currency?: string;
  dueDate?: string;
  emailSenderName?: string;
  expiryDate?: string;
  guid?: string;
  invoiceDate?: string;
  messagingMode?: string;
  paymentBillerAction?: string;
  /**
   * 支付请求URL
   */
  paymentRequestUrl?: string;
  status?: string;
}
