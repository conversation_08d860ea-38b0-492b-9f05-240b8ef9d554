export interface PaymentProcessRequest {
  amount?: number;
  /**
   * authKey
   */
  authKey?: string;
  /**
   * 业务单号
   */
  businessNo?: string;
  customerId?: string;
  extRemark?: string;
  firstName?: string;
  lastName?: string;
  memberId?: string;
  memberName?: string;
  operatorName?: string;
  operatorNo?: string;
  payType?: number;
  redirectionUrl?: string;
  remark?: string;
  storeId?: string;
}
