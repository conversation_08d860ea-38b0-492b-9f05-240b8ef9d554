import { OrderStatus } from '@/pages/sales/order/list/types/OrderStatus';
import { PayStatus } from '@/pages/sales/order/list/types/PayStatus';
import { Button, Dropdown, Space } from 'antd';
import { dropDownOption } from '@/pages/sales/order/detail/types/bpoint.type';
import { createPayment } from '@/pages/sales/order/list/services';
import { DownOutlined } from '@ant-design/icons';
import { OrderListItemEntity } from '@/pages/sales/order/list/types/order.list.item.entity';
import { useState } from 'react';
import PaymentModal, { PaymentModalProps } from '@/components/PaymentModal';
import { ButtonProps } from 'antd/es/button';
import { PaymentBizType } from '@/pages/sales/order/list/types/create.payment.request';

export interface BPointButtonProps {
  orderDetail: OrderListItemEntity;
  onSuccess?: () => void;
  buttonProps?: ButtonProps;
}

export default function BPointButton(props: BPointButtonProps) {
  const { orderDetail, onSuccess, buttonProps = {} } = props;
  // BPoint支付信息
  const [paymentModalProps, setPaymentModalProps] = useState<PaymentModalProps>({
    modalData: {
      open: false,
    },
  });
  const [payLoading, setPayLoading] = useState(false);

  return (
    <div>
      {[OrderStatus.WAIT_TO_OUTBOUND, OrderStatus.OUTBOUND_FINISH].includes(
        orderDetail?.orderStatus?.orderStatus!,
      ) &&
        [PayStatus.WAIT_TO_PAY].includes(orderDetail?.orderStatus?.payStatus!) &&
        [1].includes(orderDetail?.orderPayList?.[0]?.payType!) && (
          <Dropdown
            disabled={payLoading}
            menu={{
              items: dropDownOption,
              onClick: (item) => {
                const payType = Number(item.key);
                setPayLoading(true);
                createPayment({
                  payType,
                  bizType: PaymentBizType.Sale,
                  amount: orderDetail?.orderPrice?.shouldTotalOrderAmountYuan,
                  businessNo: orderDetail?.orders?.orderNo,
                  redirectionUrl: `${window.location.origin}/order/detail?orderNo=${orderDetail.orders?.orderNo}`,
                  storeId: orderDetail?.orders?.storeId,
                  customerId: orderDetail?.orders?.cstId,
                })
                  .then((res) => {
                    if (res) {
                      setPaymentModalProps({
                        modalData: {
                          open: true,
                          onCancel: () => setPaymentModalProps({ modalData: { open: false } }),
                        },
                        // @ts-ignore
                        payData: res,
                        payType,
                        onSuccess: onSuccess,
                        cstName: orderDetail?.orders?.cstName,
                        bizNo: orderDetail?.orders?.orderNo,
                        amount: orderDetail?.orderPrice?.shouldTotalOrderAmountYuan,
                      });
                    }
                  })
                  .finally(() => {
                    setPayLoading(false);
                  });
              },
            }}
          >
            <Button type="primary" {...buttonProps} loading={payLoading}>
              <Space>
                BPOINT
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
        )}
      <PaymentModal {...paymentModalProps} />
    </div>
  );
}
