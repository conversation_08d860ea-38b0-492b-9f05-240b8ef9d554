import { ImageItem } from '@/pages/sales/returns/operation/types/ReturnsAfterSaleDetailEntity';
import { PayChannel } from '@/components/PaymentForm/types/PayChannel';
import { CauseType } from '@/pages/sales/returns/operation/types/cause.type';

export interface ReturnsGoodsCreateItemType {
  refund?: {
    refundType?: number;
  };
  main?: ReturnsGoodsCreateMain;
  /** 商品信息 */
  items?: ReturnsGoodsCreateItem[];
  /** 订单ID */
  orderId?: string;
  /** 业务单号 */
  orderNo?: string;
  extRemark?: string;
}
export interface ReturnsGoodsCreateMain {
  /** 退货入库仓ID */
  backWarehouseId?: string;
  /** 门店ID */
  storeId?: string;
  /** 客户ID */
  cstId?: string;
  /** 备注 */
  remark?: string;
  referenceNo?: string;
  currency?: string;
  exchangeRate?: string;
  gstExcluded?: 0 | 1;
  tagIds?: number[];
}

export interface ReturnsGoodsModifyItem {
  id?: string;
  refundNum: number;
  unitAmount: number;
  cause?: string;
  orderId: string;
  orderNo: string;
  /**
   * causeType
   */
  causeType?: CauseType;
}
export interface ReturnsGoodsAddItem {
  items: ReturnsGoodsCreateItem[];
  orderId: string;
  orderNo: string;
}
export interface ReturnsGoodsCreateItem {
  /** ID */
  id?: string;
  /** 商品ID */
  itemId: string;
  /** 退货数量 */
  refundNum?: number;
  /** 退货价格 */
  unitAmount?: number;

  /** 原始销售单成本价（单位元） (销售单退货) */
  costAmount?: number;
  /** 原始销售数量 */
  saleNum?: number;
  /** 原始业务单 */
  orgOrderNo?: string;
  isGift?: 0 | 1;
}

export interface ReturnsOrderModifyRefund {
  refundType?: number;
  /** 订单ID */
  orderId?: string;
  /** 业务单号 */
  orderNo?: string;
  refundDetails?: ReturnsOrderModifyRefundDetail[];
  remark?: string;
  /**
   * 调整金额
   */
  adjustment?: number;
  /**
   * 调整原因
   */
  adjustmentReason?: string;
  returnRemark?: string;
  innerRemark?: string;
  isPrint?: boolean;
}
export interface ReturnsOrderModifyRefundDetail {
  /**
   * 退货金额(单位元)
   */
  refundAmount: number;
  refundChannel: PayChannel;
  /**
   * 退款账户名称
   */
  accountName?: string;
  /**
   * 退款账户
   */
  accountId?: string;
}

// 修改订单信息（支持备注，支付方式，返回仓库的修改）
export interface ReturnsOrderModifyType {
  refund?: ReturnsOrderModifyRefund;
  main?: ReturnsGoodsCreateMain;
  /** 商品信息 */
  items?: ReturnsGoodsCreateItem[];
  /** 订单ID */
  orderId?: string;
  /** 业务单号 */
  orderNo?: string;
  remark?: string;
  adjustmentInfo?: {
    /**
     * 调整金额
     */
    adjustment: number;
    /**
     * 调整原因
     */
    adjustmentReason: string;
  };
  remarkInfo?: RemarkInfo;
  image?: {
    url?: string;
    id?: string;
    modifyType: 1 | 2;
  };
}

export interface RemarkInfo {
  /**
   * id
   */
  id?: string;
  /**
   * 0不打印1打印
   */
  isPrint?: 0 | 1;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 1：退货备注2内部备注
   */
  remarkType?: 1 | 2;
}
