import { CauseType } from '@/pages/sales/returns/operation/types/cause.type';

export interface AfterSaleOrderRo {
  times: AfterSaleOrderTimeRo[];
  goods: AfterSaleOrderGoodsRo[];
  refunds: AfterSaleOrderRefundRo[];
  main: AfterSaleOrderMainRo;
  status: AfterSaleOrderStatusRo;
  refundDetails: AfterSaleRefundDetailRo[];
  notes: Note[];
  images?: ImageItem[];
}

export interface Note {
  /**
   * 主键
   */
  id?: string;
  /**
   * 0不打印1打印
   */
  isPrint?: number;
  /**
   * noteDetail
   */
  noteDetail?: string;
  /**
   * noteTime
   */
  noteTime?: string;
  /**
   * noteType
   */
  noteType?: number;
  /**
   * 销售单ID
   */
  orderId?: string;
  /**
   * 销售单号
   */
  orderNo?: string;
}

export interface AfterSaleOrderTimeRo {
  /** 订单ID */
  orderId?: string;
  /** 订单号 */
  orderNo?: string;
  /** 时间类型 */
  timeType?: number;
  /** 具体时间 */
  time?: string;
  /** 主键ID */
  id?: string;
  /** 时间类型名称 */
  timeTypeName?: string;
  /** 操作人工号 */
  operatorNo?: string;
  /** 操作人名 */
  operatorName?: string;
}

export interface AfterSaleOrderGoodsRo {
  returnsId: string;
  /** 订单ID */
  orderId?: string;
  locationCode?: string;
  /** 原始销售单成本价（单位：元） */
  costAmount?: number;
  /** 退货数量 */
  refundNum: number;
  unitAmountTax: number;
  /** 订单号 */
  orderNo?: string;
  /** 商品ID */
  itemId: string;
  /** 商品售价（单位：元） */
  itemAmount?: number;
  /** 商品名称 */
  itemName?: string;
  /** 商品编码 */
  itemSn?: string;
  /** 供应商编码 */
  brandPartNos?: string[];
  /** SKU ID */
  skuId?: string;
  /** 品牌ID */
  brandId?: string;
  /** 品牌 */
  brandName?: string;
  /** SKU 名称 */
  skuName?: string;
  /** 商品单位ID */
  unitId?: string;
  /** 商品单位 */
  unitName?: string;
  /** 原始业务单号 */
  orgOrderNo: string;
  isDiscount?: boolean;
  /** 三级分类 */
  categoryName?: string;
  /** 商品售价（单位：元） */
  unitAmount: number;
  /** 三级品类ID */
  categoryId?: string;
  /** 退货原因 */
  cause?: string;
  causeType?: CauseType;
  /** 原始销售数量 */
  saleNum?: number;
  /** OE 号 */
  oeNos?: string[];
  /** 主键ID */
  id: string;
  /** 门店名称 */
  storeName?: string;
}

export interface AfterSaleOrderRefundRo {
  /** 退款时间 */
  refundTime?: Date;
  /** 退款类型 */
  refundType?: number;
  /** 退款类型名称 */
  refundTypeName: string;
  /** 流水ID */
  id?: string;
  /** 订单ID */
  orderId?: string;
  /** 订单号 */
  orderNo?: string;
}

export interface AfterSaleOrderMainRo {
  /** 退货入库仓ID */
  backWarehouseId: string;
  /** 退货入库仓名称 */
  backWarehouseName: string;
  /** 订单标签 */
  orderTag?: string;
  /** 创建时间 */
  orderCreateTime: string;
  /** 客户ID */
  cstSn?: string;
  /** 售后完成时间 */
  orderFinishTime?: Date;
  /** 主键ID */
  id?: string;
  /** 销售单ID */
  orderId?: string;
  /** 销售单号 */
  orderNo?: string;
  /** 退货金额 */
  orderAmount: number;
  totalTaxationAmount?: number;
  adjustment?: number;
  refundAmount?: number;
  /** 零售商ID */
  memberId: string;
  memberName?: string;
  /** 客户ID */
  cstId: string;
  /** 客户名称 */
  cstName: string;
  /** 门店ID */
  storeId: string;
  /** 门店名称 */
  storeName: string;
  /** 制单人ID */
  salesmanId: string;
  /** 制单人 */
  salesmanName: string;
  /** 备注 */
  remark: string;
  tagIds: number[];
  currency?: string;
  exchangeRate?: number;
  gstExcluded?: 0 | 1;
  referenceNo?: string;
  belongingStoreName?: string;
  saleCompanyName?: string;
}

export interface AfterSaleOrderStatusRo {
  /** 主键ID */
  id: string;
  /** 结算状态 */
  settleStatus: number;
  /** 订单状态 */
  orderStatus: number;
  /** 销售单ID */
  orderId: string;
  /** 销售单号 */
  orderNo: string;
  /** 退款状态 */
  refundStatus: number;
  /** 结算状态名称 */
  settleStatusName: string;
  /** 退款状态名称 */
  refundStatusName: string;
  /** 单据状态名称 */
  orderStatusName: string;
}

export interface AfterSaleRefundDetailRo {
  /** 退货金额 */
  refundAmount?: number;
  /** 交易账户名 */
  accountName?: string;
  /** 交易账户ID */
  accountId?: string;
  /** 订单ID */
  orderId?: string;
  /** 订单号 */
  orderNo?: string;
  /** 退款大类名称 */
  refundTypeName?: string;
  /** 退款时间 */
  refundTime?: Date;
  /** 外部交易号 */
  tradeNo?: string;
  /** 退款状态 */
  refundStatus?: number;
  /** 退款状态名称 */
  refundStatusName?: string;
  /** 退款大类 */
  refundType?: number;
  /** 主键ID */
  id?: string;
}

export interface ImageItem {
  /**
   * 主键
   */
  id?: string;
  /**
   * 销售单ID
   */
  orderId?: string;
  /**
   * 销售单号
   */
  orderNo?: string;
  /**
   * url
   */
  url?: string;
}
