import ConfirmModal from '@/components/ConfirmModal';
import GoodsSearch from '@/components/GoodsSearch';
import { GoodsSearchBizType } from '@/components/GoodsSearch/types/BizType';
import LeftTitle from '@/components/LeftTitle';
import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { getCstDetail, getCstList } from '@/pages/customer/list/services';
import { queryMemberAccountPage } from '@/pages/finance/customer/services';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { queryStoreByAccount } from '@/pages/personnel/user/services';
import { CashRefundTypeValueEnum, RefundTypeEnum, RefundTypeValueEnum } from '@/types/CommonStatus';

import { KeepAliveTabContext } from '@/layouts/context';
import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import type { BaseOptionsType } from '@/types/BaseOptionsType';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { DeleteOutlined, InfoCircleOutlined, PlusOutlined } from '@ant-design/icons';
import {
  FormListActionType,
  PageContainer,
  ProCard,
  ProForm,
  ProFormCheckbox,
  ProFormDependency,
  ProFormDigit,
  ProFormItem,
  ProFormList,
  ProFormMoney,
  ProFormRadio,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { history, useIntl, useLocation } from '@umijs/max';
import { useAsyncEffect, useDebounceFn, useRequest } from 'ahooks';
import { FormInstance, GetProps, Upload } from 'antd';
import { Button, Checkbox, Col, ConfigProvider, Flex, message, Row, Space, Spin } from 'antd';
import type { DefaultOptionType } from 'antd/es/select';
import { defaultTo, isEmpty, isUndefined, set, sum, uniqueId, values } from 'lodash';
import React, { useContext, useEffect, useRef, useState } from 'react';
import ReturnsDetailColumns from './config/ReturnsDetailColumns';
import ReturnsOrdersColumns from './config/ReturnsOrdersColumns';
import {
  addItem,
  confirmRefund,
  createOrder,
  deleteItem,
  directIn,
  getAfterSaleDetail,
  getRefundablePaged,
  modifyItem,
  modifyOrder,
  submitOrder,
} from './services';
import type { AfterSaleOrderGoodsRo, AfterSaleOrderRo } from './types/ReturnsAfterSaleDetailEntity';
import type {
  ReturnsGoodsCreateMain,
  ReturnsOrderModifyRefund,
  ReturnsOrderModifyRefundDetail,
  ReturnsOrderModifyType,
} from './types/ReturnsGoodsCreateItemType';
import type { ReturnsOrderEntity } from './types/ReturnsOrderEntity';
import type { ReturnsOrderQuery } from './types/ReturnsOrderQuery';
import type { ReturnsOrderType } from './types/ReturnsOrderType';
import ProFormCurrency from '@/components/ProFormItem/ProFormCurrency';
import { CustomerEntity } from '@/pages/customer/list/types/CustomerEntity';
import { ProFormGroup, ProFormText } from '@ant-design/pro-form';
import { ReturnType, returnTypeOptions } from '@/pages/sales/returns/operation/types/return.type';
import CstDetail from '@/pages/sales/order/edit/compoments/CstDetail';
import { useSearchParams } from '@@/exports';
import MoneyText from '@/components/common/MoneyText';
import { compressImage } from '@/utils/fileUtils';
import { ADVANCE_ACCOUNT, getAccountList } from '@/components/PaymentForm';
import { PayChannel } from '@/components/PaymentForm/types/PayChannel';
import { CauseType } from './types/cause.type';
import { querySaleOrgList } from '@/pages/system/store/services';
import { SaleOrgEntity } from '@/pages/system/store/types/sale.org.entity';
import { REQUIRED_RULES } from '@/utils/RuleUtils';

type TabKeyType = 'goods' | 'orders';
const ReturnsOperation = () => {
  const intl = useIntl();
  const [searchParams] = useSearchParams();

  const orderId = searchParams.get('orderId') || undefined;
  const orderNo = searchParams.get('orderNo') || undefined;

  const plainOptions = [
    { label: intl.formatMessage({ id: 'sales.returns.operation.confirmSettlement' }), value: '0' },
    { label: intl.formatMessage({ id: 'sales.returns.operation.directInbound' }), value: '1' },
    { label: intl.formatMessage({ id: 'sales.returns.operation.printAfterSubmit' }), value: '2' },
  ];
  const [tabelId, setTableId] = useState<string>('returns_table_1');
  // 加载中
  const [loading, setLoading] = useState<boolean>(false);
  // 退货单明细
  // 可编辑行
  const [editorRows, setEditorRows] = useState<string[]>([]);
  // 头部表单值
  const [formValues, setFormValues] = useState<ReturnsGoodsCreateMain>();
  const [orderEditorRows, setOrderEditorRows] = useState<string[]>([]);
  // 退货明细
  const [dataSourceCache, setDataSourceCache] = useState<AfterSaleOrderGoodsRo[]>([]);
  // 退货明细编辑行
  const [editorDataSourceCache, setEditorDataSourceCache] = useState<AfterSaleOrderGoodsRo[]>([]);
  // 商品明细[编辑行]
  const [orderDataCache, setOrderDataCache] = useState<ReturnsOrderEntity[]>([]);
  const formRef = useRef<FormInstance<ReturnsGoodsCreateMain>>();
  const bottomFormRef = useRef<FormInstance<ReturnsOrderModifyRefund>>();

  // 退货性质
  const [returnType, setReturnType] = useState<ReturnType>(ReturnType.Good);

  const { state } = useLocation();
  console.log('state', state);

  // 是否为新增退货单
  const [isAdd, setIsAdd] = useState<boolean>(false);
  useEffect(() => {
    setIsAdd(isEmpty(orderId) && isEmpty(orderNo));
  }, [orderId, orderNo]);

  // 商品总数
  const [totalCount, setTotalCount] = useState<number>(0);
  const [tabActiveKey, setTabActiveKey] = useState<TabKeyType>('orders');
  // 客户信息详情
  const [cstDetail, setCstDetail] = useState<CustomerEntity>();
  // 退货单详情
  const [returnOrderDetail, setReturnOrderDetail] = useState<AfterSaleOrderRo>();
  // 上传退货图片
  const [uploadFileList, setUploadFileList] = useState<any[]>([]);
  const actionRef = useRef<ActionType>();
  const formListActionRef = useRef<FormListActionType>();
  const onChange = (key: TabKeyType) => {
    setTabActiveKey(key);
  };
  const { closeTab } = useContext(KeepAliveTabContext);

  const [checkedList, setCheckedList] = useState<string[]>([]);
  const onCheckboxChange = (list: string[]) => {
    setCheckedList(list);
  };

  const [confirmModalProps, setConfirmModalProps] = useState<GetProps<typeof ConfirmModal>>({
    open: false,
  });

  useEffect(() => {
    if (orderId && orderNo) {
      getDetail({ orderId, orderNo });
    }
  }, [orderId, orderNo]);

  /** 查询退货详情*/
  const getDetail = async (params: ReturnsOrderType) => {
    if (params.orderId && params.orderNo) {
      const result = await getAfterSaleDetail(params);
      if (result?.main) {
        const {
          tagIds,
          cstId,
          cstName,
          backWarehouseId,
          backWarehouseName,
          storeId,
          storeName,
          referenceNo,
          currency,
          exchangeRate,
          saleCompanyId,
          saleCompanyName,
        } = result.main;
        // 给你加了这个，如果存在1=销售单退回 存在2=商品退货
        if (tagIds.includes(1)) {
          setTabActiveKey('orders');
        }
        if (tagIds.includes(2)) {
          setTabActiveKey('goods');
        }
        if (tagIds.includes(3)) {
          setReturnType(ReturnType.Good);
        }
        if (tagIds.includes(4)) {
          setReturnType(ReturnType.Broken);
        }
        setWarehouseOptions([{ label: backWarehouseName, value: backWarehouseId }]);
        setStoreOptions([{ label: storeName, value: storeId }]);
        // 设置客户/退货门店/收货仓库默认值
        formRef.current?.setFieldsValue({
          cstId,
          backWarehouseId,
          storeId,
          referenceNo,
          currency,
          rate: exchangeRate,
          saleCompanyId,
          saleCompanyName,
        });
        setFormValues({ cstId, backWarehouseId, storeId });
        setReturnOrderDetail(result);
        setUploadFileList(
          result?.images?.map((item) => ({
            uid: item.id,
            url: item.url,
            state: 'done',
          })) ?? [],
        );
        bottomFormRef.current?.setFieldsValue({
          returnRemark: result.notes?.find((item) => item.noteType === 1)?.noteDetail,
          isPrint: result.notes?.find((item) => item.noteType === 1)?.isPrint,
          innerRemark: result.notes?.find((item) => item.noteType === 2)?.noteDetail,
        });
      }
      // 设置退货明细
      if (result?.goods) {
        setTotalCount(sum(result?.goods.map((t) => t.refundNum)));
        setDataSourceCache(
          result?.goods.map((t) => ({
            ...t,
            storeName: result.main.storeName,
            returnsId: t.orgOrderNo + t.itemId,
          })) ?? [],
        );
        setEditorRows(result.goods?.map((item) => item.id));
        setTableId(() => uniqueId('returns_'));
      }
      bottomFormRef.current?.setFieldsValue({
        adjustment: -result.main?.adjustment,
        adjustmentReason: result.main?.adjustmentReason,
      });
      // 设置结算信息
      if (result?.refunds) {
        const refundType = result.refunds[0].refundType;
        bottomFormRef.current?.setFieldValue('refundType', refundType);
        if (refundType == RefundTypeEnum.Cash) {
          if (result?.refundDetails) {
            bottomFormRef.current?.setFieldValue(
              'refundDetails',
              result.refundDetails.map((t) => {
                if (t.refundChannel === PayChannel.ADVANCE) {
                  return {
                    accountId: ADVANCE_ACCOUNT,
                    refundAmount: t.refundAmount,
                  };
                } else {
                  return {
                    accountId: t.accountId,
                    refundAmount: t.refundAmount,
                  };
                }
              }),
            );
          }
        }
      }
    } else {
      setIsAdd(true);
      setEditorRows([]);
      setReturnOrderDetail(undefined);
      setDataSourceCache([]);
      setTotalCount(0);
      setReturnOrderDetail(undefined);
      setCstDetail(undefined);
      bottomFormRef.current?.resetFields();
      formRef.current?.resetFields(['cstId']);
    }
  };

  const [refundOptions, setRefundOptions] = useState<DefaultOptionType[]>([]);
  // 选择客户时查询客户详情
  useAsyncEffect(async () => {
    if (formValues?.cstId) {
      const res = await getCstDetail({ cstId: formValues.cstId });
      setCstDetail(res);
      if (res?.settle?.credit) {
        setRefundOptions(RefundTypeValueEnum);
        // 新增时才需要默认值
        if (isAdd) {
          bottomFormRef.current?.setFieldValue('refundType', RefundTypeEnum.Account);
        }
      } else {
        setRefundOptions(CashRefundTypeValueEnum);
        // 新增时才需要默认值
        if (isAdd) {
          bottomFormRef.current?.setFieldValue('refundType', RefundTypeEnum.Cash);
          bottomFormRef.current?.setFieldValue('refundDetails', []);
        }
      }
    }
  }, [formValues?.cstId, isAdd]);

  /**
   * 删除
   */
  const onRemoveClick = async (row: AfterSaleOrderGoodsRo) => {
    const data = await deleteItem({ ids: [row.id], orderId, orderNo });
    if (data) {
      getDetail({ orderId, orderNo });
    }
  };

  const checkAmountAndCount = (item: ReturnsOrderEntity) => {
    // 退款数量
    let refundNum = 0;
    // 退货金额
    let refundAmount = item.refundAmount;
    if (tabActiveKey == 'orders') {
      if ((item?.refundAmount ?? 0) < 0) {
        message.error(intl.formatMessage({ id: 'sales.returns.operation.inputReturnAmount' }));
        return;
      }
      if (!item?.refundNum) {
        message.error(intl.formatMessage({ id: 'sales.returns.operation.inputReturnQuantity' }));
        return;
      } else {
        refundNum = item.refundNum;
      }
    } else if (tabActiveKey == 'goods') {
      if ((item?.price ?? 0) < 0) {
        message.error(intl.formatMessage({ id: 'sales.returns.operation.inputReturnAmount' }));
        return;
      } else {
        refundAmount = item.price;
      }
      if (!item?.number) {
        message.error(intl.formatMessage({ id: 'sales.returns.operation.inputReturnQuantity' }));
        return;
      } else {
        refundNum = item.number;
      }
    }
    return { refundAmount, refundNum };
  };
  /**
   * 新建退货记录（退货单退货/商品退货）
   * @param item
   */
  const handleCreate = async (itemList: any[]) => {
    const item = itemList[0];
    const v = await formRef.current?.validateFields([
      'cstId',
      'storeId',
      'backWarehouseId',
      'referenceNo',
      'currency',
      'rate',
      'saleCompanyName',
      'saleCompanyId',
    ]);
    const bottomValues = bottomFormRef.current?.getFieldsValue?.();
    const checkRes = checkAmountAndCount(item);
    if (isEmpty(checkRes)) return;
    setLoading(true);
    const { rate, ...rest } = v;
    const tagIds: number[] = [returnType];
    if (tabActiveKey === 'orders') {
      tagIds.push(1);
    }
    if (tabActiveKey === 'goods') {
      tagIds.push(2);
    }
    const result = await createOrder({
      main: {
        ...rest,
        remark: bottomValues?.remark,
        exchangeRate: rate,
        gstExcluded: cstDetail?.settle?.gstExcluded,
        tagIds,
      },
      refund: {
        refundType: bottomValues?.refundType,
      },
      items: [
        {
          itemId: item?.itemId,
          refundNum: checkRes.refundNum,
          unitAmount: checkRes.refundAmount,
          costAmount: item?.costAmount,
          saleNum: item?.saleNum,
          orgOrderNo: item?.orderNo,
          isGift: item?.isGift,
        },
      ],
    });
    if (result) {
      setLoading(false);
      message.success(intl.formatMessage({ id: 'sales.returns.operation.addSuccess' }));
      history.push(`/sales/returns/operation?orderId=${result.orderId}&orderNo=${result.orderNo}`);
    }
    setTimeout(() => {
      setLoading(false);
    }, 2000);
  };

  // 提交订单
  const onSubmit = async () => {
    const params = { orderId, orderNo };
    const result = await submitOrder(params);
    if (result) {
      // 确认退款
      if (checkedList.includes('0')) {
        await confirmRefund(params);
      }
      // 一键入库
      if (checkedList.includes('1')) {
        await directIn(params);
      }
      if (checkedList.includes('2')) {
        window.open(
          `/print?orderNo=${orderNo}&orderId=${orderId}&printType=${PrintType.salesReturnOrder}`,
        );
      }
      message.success(intl.formatMessage({ id: 'sales.returns.operation.submitSuccess' }));
      closeTab();
      history.push('/sales/returns/list');
    }
  };

  // 关闭弹窗
  const onCancel = () => {
    setConfirmModalProps((preProps) => ({
      ...preProps,
      open: false,
    }));
  };

  /**
   * 覆盖或者叠加记录
   * @param item 操作的记录
   * @param reload
   */
  const itemUpdate = async (item: {
    id: string;
    refundNum?: number;
    refundAmount?: number;
    cause?: string;
    causeType?: CauseType;
  }) => {
    const result = await modifyItem({
      id: item.id,
      orderId,
      orderNo,
      refundNum: item?.refundNum ?? 0,
      unitAmount: item?.refundAmount ?? 0,
      cause: item?.cause,
      causeType: item?.causeType,
    });
    if (result) {
      setLoading(false);
      message.success(intl.formatMessage({ id: 'sales.returns.operation.operationSuccess' }));
      onCancel();
      getDetail({ orderId, orderNo });
    }
    setTimeout(() => {
      setLoading(false);
    }, 2000);
  };
  /**
   * order 可退货订单新增行
   * @param item
   * @returns
   */
  const handleAdd = async (itemList: any[]) => {
    const item = itemList[0];
    const checkRes = checkAmountAndCount(item);
    if (isEmpty(checkRes)) return;
    if (dataSourceCache.length >= 200) {
      message.warning(intl.formatMessage({ id: 'sales.returns.operation.maxGoodsWarning' }));
      return;
    }
    // 新增商品
    const result = await addItem({
      orderId,
      orderNo,
      items: [
        {
          itemId: item.itemId, // TODO itemId不唯一
          refundNum: checkRes.refundNum,
          unitAmount: checkRes.refundAmount,
          costAmount: item?.costAmount,
          saleNum: item?.saleNum,
          orgOrderNo: item?.orderNo,
          isGift: item?.isGift,
        },
      ],
    });
    if (result) {
      message.success(intl.formatMessage({ id: 'sales.returns.operation.addSuccess' }));
      getDetail({ orderId, orderNo });
    }
  };
  /**
   * 更新退货订单
   */
  const updateOrder = async (params: ReturnsOrderModifyType, reload?: boolean) => {
    if (reload) {
      setLoading(true);
    }

    const result = await modifyOrder(params);
    if (result) {
      if (reload) {
        setLoading(false);
      }
      if (reload) {
        getDetail({ orderId, orderNo });
      }
    }
    if (reload) {
      setTimeout(() => {
        setLoading(false);
      }, 2000);
    }
  };

  const { run: runUpdateOrder } = useDebounceFn(
    (params: ReturnsOrderModifyType, reload?: boolean) => updateOrder(params, reload),
    {
      wait: 1000,
    },
  );

  // 加载门店下拉列表
  const { runAsync: loadStoreData } = useRequest(queryStoreByAccount, {
    manual: true,
    debounceWait: 200,
  });

  const [storeOptions, setStoreOptions] = useState<BaseOptionsType<string, string>[]>([]);
  useAsyncEffect(async () => {
    if (isAdd) {
      const storeSlectData = await loadStoreData({ status: 1 });
      const options = storeSlectData?.map((t) => ({ value: t.id, label: t.name }));
      if (options) {
        setStoreOptions(options);
        // 设置默认门店
        const storeId = options[0].value;
        setFormValues((pre) => ({ ...pre, storeId }));
        formRef.current?.setFieldValue('storeId', storeId);
      }
    }
  }, [isAdd]);
  // 加载收货仓库下拉列表
  const { runAsync: loadWarehouseData } = useRequest(warehouseList, {
    manual: true,
    debounceWait: 200,
  });
  const [warehouseOptions, setWarehouseOptions] = useState<BaseOptionsType<string, string>[]>([]);
  useAsyncEffect(async () => {
    if (formValues?.storeId && isAdd) {
      const { warehouseStoreRelationRoList: warehouseSlectData } = await loadWarehouseData({
        state: YesNoStatus.YES,
        storeIdList: [formValues.storeId],
      });
      const options = warehouseSlectData?.map((t) => ({
        value: t.warehouseId,
        label: t.warehouseName,
      }));
      // 设置默认仓库
      if (options && !isEmpty(options)) {
        const backWarehouseId = options[0].value;
        setWarehouseOptions(options);
        formRef.current?.setFieldValue('backWarehouseId', backWarehouseId);
        setFormValues((pre) => ({ ...pre, backWarehouseId }));
      }
    }
  }, [formValues?.storeId, isAdd]);
  // 选择门店时加载现款账户列表
  const [accountList, setAccountList] = useState<DefaultOptionType[]>([]);

  useAsyncEffect(async () => {
    if (formValues?.storeId && !isAdd && cstDetail && returnOrderDetail) {
      const result = await queryMemberAccountPage({
        belongToStore: [formValues?.storeId],
      });
      if (result?.data) {
        setAccountList(
          getAccountList({
            // @ts-ignore
            list: result.data,
            cstDetail,
            currency: returnOrderDetail?.main?.currency,
          }).list,
        );
      }
    }
  }, [formValues?.storeId, isAdd, cstDetail, returnOrderDetail]);

  const handleUploadChange = (info: any) => {
    console.log('handleUploadChange:', info);
    setUploadFileList(info.fileList);
    if (info.file.status === 'done') {
      const url = info.file?.response?.data?.[0];
      if (url) {
        runUpdateOrder(
          {
            orderId,
            orderNo,
            image: {
              modifyType: 1,
              url,
            },
          },
          true,
        );
      } else {
        message.error(info.file?.response?.msg);
      }
    } else if (info.file.status === 'error') {
      console.error('Upload error:', info.file.error);
    }
  };

  console.log('accountList', accountList);

  return (
    <PageContainer>
      <ConfigProvider
        theme={{
          token: {
            colorPrimary: '#F49C1F',
          },
          components: {
            InputNumber: {
              controlWidth: 80,
            },
          },
        }}
      >
        <ProCard bodyStyle={{ paddingTop: 16, paddingBottom: 10 }}>
          <ProForm<ReturnsGoodsCreateMain>
            formRef={formRef}
            onValuesChange={(_, _formValues) => {
              setFormValues(_formValues);
            }}
            submitter={false}
            grid={true}
            layout="vertical"
            disabled={!isAdd}
          >
            <ProFormSelect<string>
              fieldProps={{
                filterOption: false,
              }}
              colProps={{
                span: 6,
              }}
              showSearch={true}
              width={250}
              debounceTime={300}
              label={intl.formatMessage({ id: 'sales.returns.operation.customer' })}
              name="cstId"
              rules={[
                {
                  required: true,
                  message: intl.formatMessage({ id: 'sales.returns.operation.selectCustomer' }),
                },
              ]}
              request={(query) =>
                getCstList({ keyword: query.keyWords, cstStatus: 0 }).then((result) => {
                  const list = result.map((item) => ({
                    label: item.cstName,
                    value: item.cstId,
                  }));
                  return list;
                })
              }
            />
            <ProFormSelect<string>
              showSearch={false}
              width={250}
              colProps={{
                span: 6,
              }}
              rules={[
                {
                  required: true,
                  message: intl.formatMessage({
                    id: 'sales.returns.operation.selectReturnStore',
                  }),
                },
              ]}
              label={intl.formatMessage({ id: 'sales.returns.operation.returnStore' })}
              name="storeId"
              options={storeOptions}
            />
            <ProFormSelect
              width={250}
              colProps={{
                span: 6,
              }}
              label="销售主体"
              name="saleCompanyId"
              rules={[REQUIRED_RULES]}
              allowClear={false}
              dependencies={['storeId', 'cstId']}
              onChange={(value, item) => {
                formRef?.current?.setFieldsValue?.({ saleCompanyName: item.label });
              }}
              request={(query) => {
                if (!query.storeId || !query.cstId) {
                  return [];
                }
                return querySaleOrgList({ storeId: query.storeId, cstId: query.cstId }).then(
                  (result) => {
                    const list: any[] =
                      // @ts-ignore
                      result?.map((item: SaleOrgEntity) => ({
                        label: item.saleOrgName,
                        value: item.saleOrgId,
                        isDefault: item.isDefault,
                      })) ?? [];
                    // 查看是否有默认值
                    if (!orderNo) {
                      let defaultItem = list.find((item) => item.isDefault);
                      if (defaultItem) {
                        if (!defaultItem) {
                          defaultItem = list?.[0];
                        }
                        if (defaultItem) {
                          formRef?.current?.setFieldsValue?.({
                            saleCompanyId: defaultItem.value,
                            saleCompanyName: defaultItem.label,
                          });
                        }
                      }
                    }
                    return list;
                  },
                );
              }}
            />
            <ProFormSelect
              showSearch={false}
              width={250}
              colProps={{
                span: 6,
              }}
              rules={[
                {
                  required: true,
                  message: intl.formatMessage({
                    id: 'sales.returns.operation.selectReturnWarehouse',
                  }),
                },
              ]}
              label={intl.formatMessage({ id: 'sales.returns.operation.returnWarehouse' })}
              name="backWarehouseId"
              options={warehouseOptions}
            />
            <ProFormText
              name="referenceNo"
              label="Reference No."
              width={250}
              colProps={{
                span: 6,
              }}
            />
            {cstDetail?.settle?.isMultiCurrency === 1 && (
              <ProFormCurrency
                disabled={Boolean(orderNo)}
                width={250}
                colProps={{
                  span: 6,
                }}
              />
            )}
            <ProFormText hidden name="saleCompanyName" />
          </ProForm>
          <div className="-mt-4 mb-4">
            <CstDetail cstDetail={cstDetail} />
          </div>
        </ProCard>
        <ProCard className="mt-4" bodyStyle={{ paddingTop: 24, paddingBottom: 0 }}>
          <ProForm disabled={orderId} layout={'vertical'} grid={true} submitter={false}>
            <ProFormRadio.Group
              label={<span className="font-medium">退货性质</span>}
              fieldProps={{
                value: returnType,
                onChange: (e) => setReturnType(e.target.value),
              }}
              options={returnTypeOptions}
              colProps={{
                span: 6,
              }}
            />
            <ProFormRadio.Group
              fieldProps={{
                value: tabActiveKey,
                onChange: (e) => onChange(e.target.value),
              }}
              label={<span className="font-medium">退货原因</span>}
              options={[
                {
                  label: intl.formatMessage({ id: 'sales.returns.operation.salesOrderReturn' }),
                  value: 'orders',
                },
                {
                  label: intl.formatMessage({ id: 'sales.returns.operation.goodsReturn' }),
                  value: 'goods',
                },
              ]}
              colProps={{
                span: 6,
              }}
            />
          </ProForm>
        </ProCard>
        <div className="mx-[24px] bg-gray-100 -mt-[1PX] relative h-[1PX]" />
        <Spin spinning={loading}>
          {/* 退货单明细 */}
          {tabActiveKey == 'orders' && (
            <FunProTable<ReturnsOrderEntity, ReturnsOrderQuery>
              key="reset-ant-pro-table-search"
              className="reset-ant-pro-table-search"
              form={{ layout: 'vertical' }}
              manualRequest={true}
              requestPage={async (params) => {
                if (tabActiveKey == 'orders') {
                  try {
                    const v = await formRef.current?.validateFields([
                      'cstId',
                      'storeId',
                      'backWarehouseId',
                    ]);
                    if (values(v).length == 3) {
                      const result = await getRefundablePaged({ ...v, ...params });
                      if (result) {
                        const { data, total } = result;
                        if (data) {
                          setOrderEditorRows(
                            result.data.map((item) => `${item.orderNo}${item.itemId}`),
                          );
                          return {
                            data: data.map((t) => ({
                              ...t,
                              refundNum: defaultTo(t?.refundableNum, 0) >= 1 ? 1 : 0,
                              refundAmount: t.unitAmount,
                              returnsId: t.orderNo + t.itemId,
                            })),
                            success: true,
                            total,
                          };
                        }
                      }
                    }
                  } catch (error) {
                    console.error(error);
                  }
                }
                return { data: [], success: true };
              }}
              editable={{
                type: 'single',
                editableKeys: orderEditorRows,
                actionRender: () => [],
                onValuesChange: (record, recordList) => {
                  setOrderDataCache(recordList);
                },
              }}
              onDataSourceChange={(dataSource) => {
                setOrderDataCache(dataSource);
              }}
              rowKey="returnsId"
              options={false}
              columns={ReturnsOrdersColumns({
                addedItemPurchaseOrderNo: dataSourceCache?.map((item) => item.returnsId),
                handleAdd: (item) => (isEmpty(orderId) ? handleCreate([item]) : handleAdd([item])),
                orderDataCache,
              })}
            />
          )}
          {/* 商品明细 */}
          {tabActiveKey == 'goods' && (
            <ProCard bordered={false} bodyStyle={{ marginTop: -6 }}>
              <GoodsSearch
                warehouseId={formValues?.backWarehouseId}
                bizType={GoodsSearchBizType.SalesReturn}
                addedItemSns={dataSourceCache?.map((item) => item.itemSn ?? '')}
                onAdd={(itemList) =>
                  isEmpty(orderId) ? handleCreate(itemList) : handleAdd(itemList)
                }
              />
            </ProCard>
          )}

          {/* 退货明细 */}
          <FunProTable<AfterSaleOrderGoodsRo, any>
            key={tabelId}
            className="mt-4"
            rowKey="id"
            pagination={false}
            headerTitle={
              <div>
                <Space size={16}>
                  <LeftTitle
                    title={intl.formatMessage({ id: 'sales.returns.operation.returnDetails' })}
                  />
                  {orderNo && (
                    <span className="font-normal text-base ml-8">
                      {intl.formatMessage({ id: 'sales.returns.operation.returnOrderNo' })}:{' '}
                      {orderNo}
                    </span>
                  )}
                  {returnOrderDetail?.status?.orderStatusName && (
                    <span className="font-normal text-base ml-8">
                      {intl.formatMessage({ id: 'sales.returns.operation.returnStatus' })}:{' '}
                      {returnOrderDetail?.status?.orderStatusName}
                    </span>
                  )}
                </Space>
                {returnOrderDetail?.goods?.some((item) => item.isDiscount) && (
                  <div className="text-red-500 text-base mt-2 flex items-center gap-1 whitespace-nowrap">
                    <InfoCircleOutlined />
                    本次退货存在优惠订单，请注意查看订单详情内的活动/优惠/赠品信息，避免损失。
                  </div>
                )}
              </div>
            }
            onRow={(record) => {
              return {
                onBlur: () => {
                  const result = editorDataSourceCache.find((t) => t.returnsId == record.returnsId);
                  if (result) {
                    const { unitAmount, refundNum, cause, causeType } = result;
                    itemUpdate({
                      id: result.id,
                      refundAmount: unitAmount,
                      refundNum: refundNum,
                      cause: cause,
                      causeType,
                    });
                  }
                },
              };
            }}
            editable={{
              editableKeys: editorRows,
              onValuesChange: (_, vv) => {
                setEditorDataSourceCache(vv);
              },
              actionRender: (row) => {
                return [
                  <Button
                    className="px-0"
                    type="link"
                    key="delete"
                    onClick={() => onRemoveClick(row)}
                  >
                    {intl.formatMessage({ id: 'sales.returns.operation.delete' })}
                  </Button>,
                ];
              },
            }}
            search={false}
            actionRef={actionRef}
            columns={ReturnsDetailColumns()}
            dataSource={dataSourceCache}
          />
          <ProCard bodyStyle={{ paddingBottom: 0 }}>
            <ProForm<ReturnsOrderModifyRefund>
              key="bottomForm"
              formRef={bottomFormRef}
              validateTrigger="onBlur"
              submitter={false}
              disabled={isAdd}
              onValuesChange={async (
                changeValues: ReturnsOrderModifyRefund,
                allValues: ReturnsOrderModifyRefund,
              ) => {
                try {
                  if (changeValues.refundType) return;
                  const bottomValues = await bottomFormRef.current?.validateFields?.();
                  if (
                    typeof changeValues?.adjustment !== 'undefined' ||
                    typeof changeValues?.adjustmentReason !== 'undefined'
                  ) {
                    if (
                      typeof allValues.adjustment !== 'undefined' &&
                      typeof allValues?.adjustmentReason !== 'undefined'
                    ) {
                      runUpdateOrder(
                        {
                          orderId,
                          orderNo,
                          adjustmentInfo: {
                            adjustment: -allValues.adjustment,
                            adjustmentReason: allValues?.adjustmentReason,
                          },
                        },
                        true,
                      );
                    }
                    return;
                  }
                  if (typeof changeValues.returnRemark !== 'undefined') {
                    runUpdateOrder({
                      orderId,
                      orderNo,
                      remarkInfo: {
                        id: returnOrderDetail?.notes?.find((item) => item.noteType === 1)?.id,
                        remarkType: 1,
                        remark: changeValues.returnRemark,
                      },
                    });
                    return;
                  }
                  if (typeof changeValues.isPrint !== 'undefined') {
                    runUpdateOrder(
                      {
                        orderId,
                        orderNo,
                        remarkInfo: {
                          id: returnOrderDetail?.notes?.find((item) => item.noteType === 1)?.id,
                          isPrint: changeValues.isPrint ? 1 : 0,
                          remarkType: 1,
                        },
                      },
                      true,
                    );
                    return;
                  }
                  if (typeof changeValues.innerRemark !== 'undefined') {
                    runUpdateOrder({
                      orderId,
                      orderNo,
                      remarkInfo: {
                        id: returnOrderDetail?.notes?.find((item) => item.noteType === 2)?.id,
                        remarkType: 2,
                        remark: changeValues.innerRemark,
                      },
                    });
                    return;
                  }
                  if (bottomValues?.refundDetails) {
                    // 如果有非法结算信息则不保存
                    const validArray: ReturnsOrderModifyRefundDetail[] = [];
                    const inValidArray: ReturnsOrderModifyRefundDetail[] = [];
                    const array = bottomValues.refundDetails;
                    for (let i = 0; i < array.length; i++) {
                      const item = array[i];
                      if (
                        !isUndefined(item) &&
                        isUndefined(item?.accountId) &&
                        isUndefined(item?.refundAmount)
                      ) {
                        continue;
                      }
                      if (
                        isEmpty(item) ||
                        isEmpty(item?.accountId) ||
                        (item.refundAmount ?? 0) <= 0
                      ) {
                        inValidArray.push(item);
                      } else {
                        if (item.accountId === ADVANCE_ACCOUNT) {
                          validArray.push({
                            refundAmount: item.refundAmount,
                            refundChannel: PayChannel.ADVANCE,
                          });
                        } else {
                          validArray.push({ ...item, refundChannel: PayChannel.CASH });
                        }
                      }
                    }
                    if (!isEmpty(inValidArray) || isEmpty(validArray)) return;
                    const reqParams = {
                      orderId,
                      orderNo,
                      refund: { refundType: allValues.refundType, refundDetails: validArray },
                    };
                    runUpdateOrder(reqParams, true);
                    return;
                  }
                } catch (e) {
                  console.log(e);
                }
              }}
            >
              <Row gutter={10} className="customer-ant-form-item-label">
                <Col span={5}>
                  <ProFormSelect<number>
                    // width={500}
                    label={intl.formatMessage({ id: 'sales.returns.operation.settlementMethod' })}
                    options={refundOptions}
                    name="refundType"
                    allowClear={false}
                    onChange={async (value) => {
                      const reqParams: ReturnsOrderModifyType = {
                        orderId,
                        orderNo,
                        refund: { refundType: value },
                      };
                      // 结算方式修改为挂账时直接更新
                      if (value == 0) {
                        const defaultAccountId = accountList?.filter((item) => !item.disabled)?.[0]
                          ?.value;
                        const refundDetails = [
                          {
                            accountId: defaultAccountId,
                            refundAmount: returnOrderDetail?.main.refundAmount,
                            refundChannel: PayChannel.CASH,
                          },
                        ];
                        if (refundDetails[0].accountId === ADVANCE_ACCOUNT) {
                          delete refundDetails[0].accountId;
                          refundDetails[0].refundChannel = PayChannel.ADVANCE;
                        }
                        set(reqParams, 'refund.refundDetails', refundDetails);
                        bottomFormRef?.current?.setFieldValue('refundDetails', refundDetails);
                      }
                      runUpdateOrder(reqParams, true);
                    }}
                  />
                  <ProFormDependency name={['refundType']}>
                    {(props) => {
                      const { refundType } = props;
                      if (refundType === 10) {
                        return (
                          <div className="text-gray-500 availableAmount">
                            {intl.formatMessage(
                              {
                                id: 'sales.returns.operation.usedAvailable',
                              },
                              {
                                used: defaultTo(cstDetail?.settle?.usedAmount, '-'),
                                available: defaultTo(cstDetail?.settle?.availableAmount, '-'),
                              },
                            )}
                          </div>
                        );
                      } else if (refundType === 0) {
                        return (
                          <ProFormList
                            className="reset-ant-form-item"
                            name="refundDetails"
                            max={2}
                            min={1}
                            initialValue={[{}]}
                            actionRef={formListActionRef}
                            creatorButtonProps={false}
                            copyIconProps={false}
                            deleteIconProps={false}
                            actionRender={(field, action, _, count) => [
                              <div key="action" className="ml-4">
                                {count == 1 && (
                                  <PlusOutlined
                                    className="text-lg"
                                    key="add"
                                    onClick={() => action.add()}
                                  />
                                )}
                                {count == 2 && (
                                  <DeleteOutlined
                                    className="text-lg text-primary"
                                    key="remove"
                                    onClick={() => action.remove(field.name)}
                                  />
                                )}
                              </div>,
                            ]}
                          >
                            <ProFormMoney
                              // width={500}
                              name="refundAmount"
                              fieldProps={{
                                addonBefore: (
                                  <ProFormSelect
                                    noStyle
                                    name="accountId"
                                    allowClear={false}
                                    options={accountList}
                                    placeholder={intl.formatMessage({
                                      id: 'sales.returns.operation.selectAccount',
                                    })}
                                    normalize={(value, prevValue, prevValues) => {
                                      const res = prevValues?.refundDetails?.find(
                                        (t: ReturnsOrderModifyRefundDetail) =>
                                          t?.accountId == value,
                                      );
                                      if (!isEmpty(res)) {
                                        message.error(
                                          intl.formatMessage({
                                            id: 'sales.returns.operation.selectDifferentAccount',
                                          }),
                                        );
                                        return prevValue ?? '';
                                      }
                                      return value;
                                    }}
                                  />
                                ),
                                controls: false,
                                precision: 2,
                                // max: totalAmount,
                                placeholder: intl.formatMessage({
                                  id: 'sales.returns.operation.inputAmount',
                                }),
                              }}
                            />
                          </ProFormList>
                        );
                      }
                    }}
                  </ProFormDependency>
                </Col>
                <Col span={4}>
                  <ProFormGroup>
                    <ProFormDigit
                      min={0}
                      label={intl.formatMessage({
                        id: 'sales.order.edit.adjustAmount',
                      })}
                      name="adjustment"
                      fieldProps={{
                        precision: 2,
                      }}
                      placeholder={intl.formatMessage({
                        id: 'sales.order.edit.adjustAmountCustomPrice',
                      })}
                    />
                    <ProFormText
                      name="adjustmentReason"
                      placeholder={intl.formatMessage({
                        id: 'sales.order.edit.adjustAmountCustomReason',
                      })}
                    />
                  </ProFormGroup>
                </Col>
                <Col span={5}>
                  <ProFormItem label="退货图片">
                    <Upload
                      name="file"
                      listType="picture-card"
                      multiple={true}
                      maxCount={2}
                      accept=".jpg,.png,.jpeg,.gif,.webp"
                      action="/apigateway/public/upload/object/batch"
                      onChange={handleUploadChange}
                      fileList={uploadFileList}
                      onRemove={(file) => {
                        return runUpdateOrder(
                          {
                            orderId,
                            orderNo,
                            image: {
                              modifyType: 2,
                              id: file.uid,
                            },
                          },
                          true,
                        );
                      }}
                      showUploadList={true}
                      beforeUpload={(file) => compressImage(file)}
                    >
                      {uploadFileList.length < 2 && (
                        <button style={{ border: 0, background: 'none' }} type="button">
                          <PlusOutlined />
                          <div style={{ marginTop: 8 }}>Upload</div>
                        </button>
                      )}
                    </Upload>
                  </ProFormItem>
                </Col>
                <Col span={5}>
                  <ProFormTextArea
                    label={
                      <div className="flex justify-between items-center whitespace-nowrap">
                        <span className="font-semibold">
                          {intl.formatMessage({ id: 'sales.order.edit.returnRemark' })}
                        </span>
                        <label className="ml-5 flex gap-2 items-center font-normal h-[20px]">
                          <div className="relative top-2.5">
                            <ProFormCheckbox name="isPrint" />
                          </div>
                          {intl.formatMessage({ id: 'sales.order.edit.printRemark' })}
                        </label>
                      </div>
                    }
                    name="returnRemark"
                    rules={[
                      {
                        message: intl.formatMessage({
                          id: 'sales.returns.operation.maxCharacters',
                        }),
                        max: 200,
                      },
                    ]}
                  />
                </Col>
                <Col span={5}>
                  <ProFormTextArea
                    label={
                      <span className="font-semibold">
                        {intl.formatMessage({ id: 'sales.order.edit.innerRemark' })}
                      </span>
                    }
                    name="innerRemark"
                    fieldProps={{
                      maxLength: 200,
                    }}
                    rules={[
                      {
                        message: intl.formatMessage({
                          id: 'sales.returns.operation.maxCharacters',
                        }),
                        max: 200,
                      },
                    ]}
                  />
                </Col>
              </Row>
            </ProForm>
          </ProCard>
          <ProCard className="mt-[1px] text-base">
            <div>
              <Flex justify="space-between">
                <Flex key="summary" gap={20} justify="flex-start" align="center">
                  <span className="text-[16px] font-semibold text-[#000000D9]">
                    {intl.formatMessage({ id: 'sales.returns.operation.totalQuantity' })}：
                    <MoneyText text={defaultTo(totalCount, '0')} />
                  </span>
                  <span className="text-[16px] font-semibold text-[#000000D9]">
                    {intl.formatMessage({ id: 'sales.returns.operation.totalGoodsAmount' })}：
                    <MoneyText text={defaultTo(returnOrderDetail?.main.orderAmount, '0')} />
                  </span>
                  <span className="text-[16px] font-semibold text-[#000000D9]">
                    GST：
                    <MoneyText text={defaultTo(returnOrderDetail?.main.totalTaxationAmount, '0')} />
                  </span>
                  <span className="text-[16px] font-semibold text-[#000000D9]">
                    {intl.formatMessage({ id: 'sales.order.edit.adjustAmount' })}：
                    <MoneyText text={-defaultTo(returnOrderDetail?.main.adjustment, '0')} />
                  </span>
                </Flex>
                <Flex>
                  <span className="flex flex-row items-center">
                    <span className="text-[16px] font-semibold text-[#000000D9]">
                      {intl.formatMessage({ id: 'sales.returns.operation.totalRefundAmount' })}：
                    </span>
                    <span className="text-[24px] font-medium text-[#F83431]">
                      <MoneyText text={defaultTo(returnOrderDetail?.main.refundAmount, '0')} />
                    </span>
                  </span>
                </Flex>
              </Flex>
              <Flex justify={'right'} className="mt-2">
                <Space>
                  <Checkbox.Group
                    disabled={dataSourceCache?.length <= 0}
                    options={plainOptions}
                    value={checkedList}
                    onChange={onCheckboxChange}
                  />
                  <AuthButton
                    disabled={dataSourceCache?.length <= 0}
                    authority="salesSubmit"
                    type="primary"
                    onClick={onSubmit}
                  >
                    {intl.formatMessage({ id: 'sales.returns.operation.submit' })}
                  </AuthButton>
                </Space>
              </Flex>
            </div>
          </ProCard>
        </Spin>
        <ConfirmModal {...confirmModalProps} />
      </ConfigProvider>
    </PageContainer>
  );
};

export default withKeepAlive(ReturnsOperation);
