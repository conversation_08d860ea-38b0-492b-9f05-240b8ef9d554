import LeftTitle from '@/components/LeftTitle';
import ProFormCurrency from '@/components/ProFormItem/ProFormCurrency';
import ProFormObject, { ObjectType } from '@/components/ProFormItem/ProFormObject';
import { ProFormUploadSingleCard } from '@/components/ProFormItem/ProFormUpload';
import FunProTable from '@/components/common/FunProTable';
import MutiCurrency from '@/components/common/MutiCurrency';
import { queryMemberAccountPage } from '@/pages/finance/customer/services';
import type { FinPayableEntity } from '@/pages/finance/payment/types/FinPayableEntity';
import CreateColumns from '@/pages/finance/supplierPayment/config/CreateColumns';
import {
  paymentConfirmation,
  queryPayableList,
  queryTotalPayable,
  TotalPayableEntity,
} from '@/pages/finance/supplierPayment/services';
import { queryStoreByAccount } from '@/pages/personnel/user/services';
import { queryFullById } from '@/pages/purchase/supplier/services';
import { localCurrencyTransfer } from '@/utils/PriceUtils';
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import withKeepAlive from '@/wrappers/withKeepAlive';
import {
  PageContainer,
  ProCard,
  ProForm,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { history, useIntl, useSearchParams } from '@umijs/max';
import { useUpdateEffect } from 'ahooks';
import { Alert, Button, Flex, Form, Input, message, Space } from 'antd';
import _ from 'lodash';
import NP from 'number-precision';
import React, { useEffect, useMemo, useState } from 'react';
import { queryOtherRelatedCompanyDetail } from '../otherRelated/service';

const { Search } = Input;

const width = 'md';

const PaymentAdd = () => {
  const intl = useIntl();
  const [searchParams] = useSearchParams();
  const serialNumber = searchParams.get('serialNumber') ?? '';

  const [form] = Form.useForm();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const [orderNo, setOrderNo] = useState<string | undefined>(); // 核销订单过滤：业务单号
  const [sellerTotalPayable, setSellerTotalPayable] = useState<TotalPayableEntity[]>([]); // 应付总额
  const [accountOptions, setAccountOptions] = useState<any>([]); // 收款账户
  const [paymentList, setPaymentList] = useState<FinPayableEntity[]>([]); // 核销订单
  const selectedPaymentList = paymentList.filter((item) => selectedRowKeys.includes(item.id!));
  const [isMultiCurrency, setIsMultiCurrency] = useState<boolean>(false); // 对币种对象

  const sellerId = Form.useWatch('sellerId', form);
  const sellerType = Form.useWatch('sellerType', form);
  const currency = Form.useWatch('currency', form);
  const rate = Form.useWatch('rate', form);
  const currencySymbol = Form.useWatch('currencySymbol', form);

  const totalPaymentAmountYuan = Form.useWatch('totalPaymentAmountYuan', form) ?? 0;
  const localTotalPaymentAmountYuan = localCurrencyTransfer(totalPaymentAmountYuan, rate);

  // 本位币
  const isLocalCurrency = currency === 'AUD' && selectedPaymentList[0]?.currency === 'AUD';
  // 跨币种
  const isCrossCurrency =
    currency !== paymentList.filter((item) => selectedRowKeys.includes(item.id!))[0]?.currency;

  // 本次核销合计
  const currTotalPaymentAmountYuan = useMemo(() => {
    return selectedPaymentList.reduce((accumulator, currentItem) => {
      return NP.plus(accumulator, Number(currentItem.currPayAmount));
    }, 0);
  }, [selectedPaymentList]);

  // 本次核销合计（本位币）
  const localCurrTotalPaymentAmountYuan = useMemo(() => {
    return selectedPaymentList.reduce((accumulator, currentItem) => {
      return NP.plus(
        accumulator,
        localCurrencyTransfer(Number(currentItem.currPayAmount), currentItem.rate),
      );
    }, 0);
  }, [selectedPaymentList]);

  const lossAmountYuan = useMemo(() => {
    const toRoundedLoss =
      NP.minus(localTotalPaymentAmountYuan, localCurrTotalPaymentAmountYuan) * 100;
    return -NP.divide(Math.round(toRoundedLoss), 100);
  }, [localTotalPaymentAmountYuan, localCurrTotalPaymentAmountYuan]);

  const autoAssignOrders = () => {
    if (new Set(paymentList.map((item) => item.currency)).size > 1) {
      message.error(
        intl.formatMessage({ id: 'finance.supplierPayment.multiCurrencynotSupportError' }),
      );
      return;
    }
    if (currency !== paymentList?.[0]?.currency) {
      message.error(
        intl.formatMessage({ id: 'finance.supplierPayment.multiCurrencynotSupportError' }),
      );
      return;
    }

    if (!totalPaymentAmountYuan == undefined) {
      message.error(intl.formatMessage({ id: 'finance.supplierPayment.noAmountError' }));
      return;
    }
    if (totalPaymentAmountYuan <= 0) {
      message.error(intl.formatMessage({ id: 'finance.supplierPayment.negativeAmountError' }));
      return;
    }

    const newSelectedKeys: React.Key[] = [];
    const displayedIds = new Set(paymentList.map((item) => item.id));
    let amountToAssign = totalPaymentAmountYuan;

    const newMasterList = paymentList.map((item) => {
      const newItem = { ...item };
      if (displayedIds.has(newItem.id)) {
        if (amountToAssign > 0) {
          const unreceived = newItem.remainPayableAmountYuan || 0;
          if (unreceived > 0) {
            if (amountToAssign >= unreceived) {
              newItem.currPayAmount = unreceived;
              amountToAssign = _.round(_.subtract(amountToAssign, unreceived), 2);
              newSelectedKeys.push(newItem.id);
            } else {
              newItem.currPayAmount = amountToAssign;
              amountToAssign = 0;
              newSelectedKeys.push(newItem.id);
            }
          } else {
            newItem.currPayAmount = null;
          }
        } else {
          newItem.currPayAmount = null;
        }
      }
      return newItem;
    });

    setPaymentList(newMasterList);
    setSelectedRowKeys(newSelectedKeys);
  };

  const handleUpdate = (record: FinPayableEntity) => {
    // Find the currency of the first selected item, if any
    let firstSelectedCurrency: string | undefined;
    if (selectedRowKeys.length > 0) {
      const firstSelectedItem = selectedPaymentList.find((item) => item.id === selectedRowKeys[0]);
      if (firstSelectedItem) {
        firstSelectedCurrency = firstSelectedItem.currency;
      }
    }

    // If trying to add a new item by entering an amount
    if (record.currPayAmount) {
      // and its currency is different from already selected items
      if (firstSelectedCurrency && record.currency !== firstSelectedCurrency) {
        message.error(
          intl.formatMessage({
            id: 'finance.supplierPayment.multiCurrencyError',
            defaultMessage: '每次付款只能核销一个币种的订单，不同币种订单请分开付款。',
          }),
        );

        // Revert the change in the list
        const revertedList = paymentList.map((item) =>
          item.id === record.id ? { ...item, currPayAmount: null } : item,
        );
        setPaymentList(revertedList);
        return; // Stop further processing
      }
    }

    const newReceivableList = paymentList.map((item) =>
      item.id === record.id ? { ...item, currPayAmount: record.currPayAmount } : item,
    );
    setPaymentList(newReceivableList);

    if (record.currPayAmount) {
      setSelectedRowKeys((keys) => [...new Set([...keys, record.id])]);
    } else {
      setSelectedRowKeys((keys) => keys.filter((key) => key !== record.id));
    }
  };

  const getPayableList = (params) => {
    queryPayableList({ ...params, payableFlag: 1 }).then((result = []) => {
      setSelectedRowKeys([]);
      setPaymentList(
        result
          ?.filter((item) => item.remainPayableAmountYuan !== 0)
          .sort((a, b) => new Date(a.billDate) - new Date(b.billDate)),
      );
      setEditableRowKeys(result?.map((item) => item.id));
    });
  };

  useEffect(() => {
    if (!sellerId) return;
    getPayableList({ sellerId, orderNo });
  }, [sellerId, orderNo]);

  useUpdateEffect(() => {
    if (sellerId) {
      queryTotalPayable({ sellerId }).then((result) => {
        setSellerTotalPayable(result);
      });
    }
  }, [sellerId]);


  useEffect(() => {
    if (sellerId && sellerType === ObjectType.Suppler) {
      queryFullById({ id: sellerId }).then((result) => {
        setIsMultiCurrency(Boolean(result?.supplierSettleInfo?.isMultiCurrency));
      });
    }
    if (sellerId && sellerType === ObjectType.OtherCompany) {
      queryOtherRelatedCompanyDetail({ id: sellerId }).then((result) => {
        setIsMultiCurrency(Boolean(result?.otherRelatedCompanyInfo?.isMultiCurrency));
      });
    }
  }, [sellerId, sellerType])

  useEffect(() => {
    if (currency) {
      queryMemberAccountPage({ pageSize: 1000, currency }).then((data) => {
        setAccountOptions(
          data?.data?.map(({ id, memberAccountName }) => ({
            value: id,
            label: memberAccountName,
          })),
        );
        form.setFieldsValue({
          paymentAccountId: data?.data?.[0]?.id,
          paymentAccountName: data?.data?.[0]?.memberAccountName,
          memberAccountName: data?.data?.[0]?.memberAccountName,
        });
      });
    }
  }, [currency]);

  return (
    <PageContainer>
      <ProForm
        form={form}
        layout="vertical"
        submitter={{
          render: (props, doms) => {
            return (
              <ProCard className="rounded-lg mt-5">
                <div className="flex justify-between items-center w-full">
                  <Flex gap={40} className="items-end">
                    <div>
                      <div className="flex flex-row items-center">
                        <span className="text-[16px] font-semibold text-[#000000D9]">
                          {intl.formatMessage({ id: 'finance.supplierPayment.paymentAmount' })}：
                        </span>
                        <span className="text-[24px] font-medium text-primary">
                          {currencySymbol}
                          {totalPaymentAmountYuan ?? 0}
                        </span>
                      </div>
                      {!isLocalCurrency && selectedPaymentList.length > 0 && (
                        <div className="flex flex-row items-center text-gray-600">
                          <span>
                            {intl.formatMessage({
                              id: 'finance.supplierPayment.summary.paymentAmountLocal',
                            })}
                            ：
                          </span>
                          <span>${_.round(localTotalPaymentAmountYuan ?? 0, 2)}</span>
                        </div>
                      )}
                    </div>
                    <div>
                      <div className="flex flex-row items-center">
                        <span className="text-[16px] font-semibold text-[#000000D9]">
                          {intl.formatMessage({ id: 'finance.supplierPayment.currentWriteOff' })}：
                        </span>
                        <span className="text-[24px] font-medium text-primary">
                          {selectedPaymentList[0]?.currencySymbol ?? ''}
                          {currTotalPaymentAmountYuan}
                        </span>
                      </div>
                      {!isLocalCurrency && selectedPaymentList.length > 0 && (
                        <div className="flex flex-row items-center text-gray-600">
                          <span>
                            {intl.formatMessage({
                              id: 'finance.supplierPayment.summary.currentWriteOffLocal',
                            })}
                            ：
                          </span>
                          <span>${_.round(localCurrTotalPaymentAmountYuan ?? 0, 2)}</span>
                        </div>
                      )}
                    </div>
                    <div>
                      <div className="flex flex-row items-center">
                        {currTotalPaymentAmountYuan != totalPaymentAmountYuan &&
                          !isCrossCurrency && (
                            <Alert
                              message={intl.formatMessage({
                                id: 'finance.supplierPayment.writeOffAmountMismatch',
                              })}
                              banner
                              className="bg-white text-yellow-500"
                            />
                          )}
                      </div>
                      {!isLocalCurrency && selectedPaymentList.length > 0 && (
                        <div className="flex flex-row items-center text-gray-600">
                          <span>
                            {intl.formatMessage({
                              id: 'finance.supplierPayment.summary.lossAmount',
                            })}
                            ：
                          </span>
                          <span
                            className={
                              lossAmountYuan === 0
                                ? 'text-gray-600'
                                : lossAmountYuan > 0
                                  ? 'text-[#F83431]'
                                  : 'text-[#00C853]'
                            }
                          >
                            {lossAmountYuan === 0 ? '' : lossAmountYuan > 0 ? '+' : '-'}$
                            {Math.abs(lossAmountYuan)}
                          </span>
                        </div>
                      )}
                    </div>
                  </Flex>

                  <Space>
                    <Button
                      key="rest"
                      type="primary"
                      ghost
                      onClick={() => history.push('/finance/supplierPayment/list')}
                    >
                      {intl.formatMessage({ id: 'common.button.cancel' })}
                    </Button>
                    <Button type="primary" key="launch" onClick={() => props.form?.submit?.()}>
                      {intl.formatMessage({ id: 'finance.supplierPayment.confirm' })}
                    </Button>
                  </Space>
                </div>
              </ProCard>
            );
          },
        }}
        onFinish={async (formData: any) => {
          const selectedOrderDetailList = paymentList
            .filter((item) => item.currPayAmount)
            .map((item) => ({
              payableId: item.id,
              paymentAmountYuan: item.currPayAmount,
              ledgerType: item.ledgerType,
            }));
          if (!selectedOrderDetailList || selectedOrderDetailList.length == 0) {
            message.warning(
              intl.formatMessage({ id: 'finance.supplierPayment.noOrderSelectedWarning' }),
            );
            return false;
          }
          if (currTotalPaymentAmountYuan != totalPaymentAmountYuan && !isCrossCurrency) {
            message.error(
              intl.formatMessage({ id: 'finance.supplierPayment.amountMismatchError' }),
            );
            return false;
          }
          const { picFileList, ...values } = formData;
          const result = await paymentConfirmation({
            ...values,
            lossAmountYuan,
            ledgerType: totalPaymentAmountYuan > 0 ? 2 : 1,
            finPaymentOrderDetailCmdList: selectedOrderDetailList,
          });
          if (result) {
            message.success(intl.formatMessage({ id: 'common.message.submitSuccess' }));
            history.push('/finance/supplierPayment');
          }
        }}
      >
        <ProCard className="rounded-lg">
          <ProForm.Group>
            <ProFormObject
              form={form}
              label={intl.formatMessage({ id: 'finance.supplierPayment.columns.supplier' })}
              required
              rules={[REQUIRED_RULES]}
              objects={[ObjectType.Suppler, ObjectType.OtherCompany]}
              fieldsName={{
                fieldType: 'sellerType',
                fieldName: 'sellerName',
                fieldId: 'sellerId',
              }}
              extra={
                Boolean(sellerTotalPayable.length) &&
                <>
                  {intl.formatMessage({
                    id: 'finance.supplierPayment.totalPayable',
                  })}:
                  <MutiCurrency amountList={sellerTotalPayable} />
                </>
              }
              disabled={serialNumber ? true : false}
            />
            <ProFormSelect
              width={width}
              required
              rules={[REQUIRED_RULES]}
              name="paymentStoreId"
              label={intl.formatMessage({ id: 'finance.supplierPayment.columns.paymentStore' })}
              onChange={(value, option) => {
                form.setFieldsValue({
                  paymentStoreName: option.label,
                });
              }}
              request={async () => {
                const data = await queryStoreByAccount({});
                if (data && data.length > 0) {
                  form.setFieldsValue({
                    paymentStoreId: data[0].id,
                    paymentStoreName: data[0].name,
                  });
                }
                return data?.map(({ id, name }) => ({
                  value: id,
                  label: name,
                }));
              }}
            />
            <ProFormText hidden name="paymentStoreName" />

            {isMultiCurrency && <ProFormCurrency
              label={intl.formatMessage({ id: 'finance.supplierPayment.paymentCurrency' })}
            />}

            <ProFormMoney
              customSymbol=" " // 空格不能去
              width={width}
              required
              name="totalPaymentAmountYuan"
              label={intl.formatMessage({ id: 'finance.supplierPayment.paymentAmount' })}
              rules={[REQUIRED_RULES]}
            />

            <ProFormSelect
              width={width}
              required
              name="paymentAccountId"
              label={intl.formatMessage({ id: 'finance.supplierPayment.columns.paymentAccount' })}
              showSearch
              rules={[REQUIRED_RULES]}
              options={accountOptions}
              onChange={(value, option) => {
                form.setFieldsValue({
                  paymentAccountName: option.label,
                  memberAccountName: option.label,
                });
              }}
            />
            <ProFormText hidden name="paymentAccountName" />
            <ProFormText hidden name="memberAccountName" />

            <ProForm.Item label=" " colon={false}>
              <span
                onClick={autoAssignOrders}
                className="cursor-pointer text-[#0099D3] flex items-center"
              >
                <img src={require('@/assets/icons/assign.png')} className="w-5 h-5 mr-1" />
                {intl.formatMessage({ id: 'finance.receive.autoAssign' })}
              </span>
            </ProForm.Item>
          </ProForm.Group>
        </ProCard>

        <FunProTable<FinPayableEntity, any>
          scroll={{ x: 'max-content' }}
          className="mt-4"
          title={() => (
            <Flex justify="space-between" align="flex-end">
              <LeftTitle
                title={intl.formatMessage({ id: 'finance.supplierPayment.writeOffOrder' })}
              />
              <Search
                placeholder={intl.formatMessage({
                  id: 'finance.supplierPayment.placeholders.businessOrderNo',
                })}
                allowClear
                onSearch={setOrderNo}
                style={{ width: 300 }}
              />
            </Flex>
          )}
          search={false}
          pagination={false}
          editable={{
            editableKeys,
          }}
          rowSelection={{
            selectedRowKeys,
            onChange: (keys, rows) => {
              if (rows.length > 1) {
                const firstCurrency = rows[0].currency;
                const allSameCurrency = rows.every((row) => row.currency === firstCurrency);
                if (!allSameCurrency) {
                  message.error(
                    intl.formatMessage({ id: 'finance.supplierPayment.multiCurrencyError' }),
                  );
                  return;
                }
              }

              const newPaymentList = paymentList.map((item) => {
                if (keys.includes(item.id)) {
                  // If newly selected, fill amount
                  if (!selectedRowKeys.includes(item.id)) {
                    return { ...item, currPayAmount: item.remainPayableAmountYuan };
                  }
                } else {
                  // If deselected, clear amount
                  if (selectedRowKeys.includes(item.id)) {
                    return { ...item, currPayAmount: null };
                  }
                }
                return item;
              });
              setPaymentList(newPaymentList);
              setSelectedRowKeys(keys);
            },
          }}
          dataSource={paymentList}
          options={false}
          columns={CreateColumns({ handleUpdate, intl })}
        />

        <ProCard>
          <Flex>
            <ProForm.Group colProps={{ span: 24 }}>
              <ProForm.Group>
                <ProFormUploadSingleCard
                  name="picFileList"
                  label={intl.formatMessage({ id: 'finance.supplierPayment.columns.paymentPic' })}
                  onChange={(fileList) => {
                    console.log(fileList);
                    form?.setFieldsValue({
                      picFileList: fileList,
                      pic: fileList?.[0]?.response?.data?.[0],
                    });
                  }}
                />
                <ProFormText name="pic" hidden />
              </ProForm.Group>
              <ProForm.Group>
                <ProFormTextArea
                  name="remark"
                  label={intl.formatMessage({ id: 'finance.supplierPayment.columns.remark' })}
                  width={600}
                  labelCol={{ span: 3 }}
                />
              </ProForm.Group>
            </ProForm.Group>
          </Flex>
        </ProCard>
      </ProForm>
    </PageContainer>
  );
};

export default withKeepAlive(PaymentAdd);
