import LeftTitle from '@/components/LeftTitle';
import ProFormCurrency from '@/components/ProFormItem/ProFormCurrency';
import ProFormObject, { ObjectType } from '@/components/ProFormItem/ProFormObject';
import { ProFormUploadSingleCard } from '@/components/ProFormItem/ProFormUpload';
import FunProTable from '@/components/common/FunProTable';
import { queryReceivableList } from '@/pages/finance/collection/services';
import type { FinReceivableEntity } from '@/pages/finance/collection/types/FinReceivableEntity.entity';
import { queryMemberAccountPage } from '@/pages/finance/customer/services';
import { getCreateColumns } from '@/pages/finance/receive/config/CreateColumns';
import { queryStoreByAccount } from '@/pages/personnel/user/services';
import { querySaleOrgList } from '@/pages/system/store/services';
import { adjustPriceByRule, localCurrencyTransfer } from '@/utils/PriceUtils';
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import withKeepAlive from '@/wrappers/withKeepAlive';
import {
  PageContainer,
  ProCard,
  ProForm,
  ProFormDependency,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { history, useIntl, useSearchParams } from '@umijs/max';
import { useUpdateEffect } from 'ahooks';
import { Button, Checkbox, Flex, Form, Input, message, Space } from 'antd';
import _ from 'lodash';
import NP from 'number-precision';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { MemberAccountEntity } from '../customer/types/MemberAccountEntity';
import {
  queryAdvanceAccount,
  queryReceivedFlowList,
  queryTotalReceivable,
  saveReceived,
  TotalReceivableEntity,
  updateReceivedStatus
} from './services';
import { AdvanceAccount } from './types/AdvanceAccount.entity';
import { AdjustType } from './types/ReceivedConfirmation';
import { ReceivedStatus } from './types/ReceivedEntity';

const { Search } = Input;

const width = 'md';

const AddReceived = () => {
  const intl = useIntl();
  const [searchParams] = useSearchParams();
  const serialNumber = searchParams.get('serialNumber') ?? '';
  const isEdit = serialNumber ? true : false;
  const cstId = searchParams.get('cstId') ?? '';
  const cstName = searchParams.get('cstName') ?? '';
  const [form] = Form.useForm();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [_receivableList, setReceivableList] = useState<FinReceivableEntity[]>([]);

  const [totalReceivable, setTotalReceivable] = useState<TotalReceivableEntity[]>([]);
  const [accountOptions, setAccountOptions] = useState<any>([]); // 收款账户
  const [advanceAccount, setAdvanceAccount] = useState<AdvanceAccount>(); // 预收账户

  const [orderNo, setOrderNo] = useState<string | undefined>(); // 核销订单过滤：业务单号
  const [overdueType, setOverdueType] = useState<('Due' | 'Over Due')[]>([]); // 核销订单过滤：已到期/已到期

  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const receivableList = useMemo(() => {
    let filterList = _receivableList ?? [];
    if (orderNo) filterList = filterList.filter((item) => item.orderNo === orderNo);
    if (Boolean(overdueType.length))
      return filterList.filter((item) => overdueType.includes(item.tag));
    return filterList;
  }, [_receivableList, overdueType, orderNo]);
  const selectedReceivableList = receivableList.filter((item) =>
    selectedRowKeys.includes(item.orderNo),
  );

  // 是否是本位币
  const currency = Form.useWatch('currency', form);
  const rate = Form.useWatch('rate', form);
  const currencySymbol = Form.useWatch('currencySymbol', form);
  const isLocalCurrency = currency === 'AUD' && selectedReceivableList?.[0]?.currency === 'AUD';
  const isCrossCurrency = currency !== selectedReceivableList?.[0]?.currency;

  const buyerId = Form.useWatch('buyerId', form);
  const buyerType = Form.useWatch('buyerType', form);
  const companyType = Form.useWatch('companyType', form);

  // 收款金额
  const watchedTotalReceivedAmountValue = Form.useWatch('totalReceivedAmountYuan', form) ?? 0;
  const localTotalReceivedAmountValue = localCurrencyTransfer(
    watchedTotalReceivedAmountValue,
    rate,
  );

  // 本次核销合计
  const currTotalReceivedAmount = useMemo(() => {
    return selectedReceivableList.reduce((accumulator, currentItem) => {
      return NP.plus(accumulator, Number(currentItem?.currReceivedAmount ?? 0));
    }, 0);
  }, [selectedReceivableList, selectedRowKeys]);

  const localCurrTotalReceivedAmount = useMemo(() => {
    return selectedReceivableList.reduce((accumulator, currentItem) => {
      return NP.plus(
        accumulator,
        localCurrencyTransfer(Number(currentItem?.currReceivedAmount ?? 0), currentItem.rate),
      );
    }, 0);
  }, [selectedReceivableList, selectedRowKeys]);

  // 调整金额
  const watchedAdjustType = Form.useWatch('adjustType', form);
  const watchedAdjustAmountValue = Form.useWatch('adjustAmountYuan', form) ?? 0;
  const adjustAmount = useMemo(() => {
    if (watchedAdjustType == AdjustType.DISCOUNT) {
      return watchedAdjustAmountValue;
    }
    // 收款币种和核销币种，只要有一个不是澳元，就不支持收款抹零
    if (watchedAdjustType == AdjustType.ROUND && isLocalCurrency) {
      return NP.minus(currTotalReceivedAmount, adjustPriceByRule(currTotalReceivedAmount));
    }
    return 0;
  }, [watchedAdjustType, currTotalReceivedAmount, watchedAdjustAmountValue]);
  const localAdjustAmountValue = localCurrencyTransfer(adjustAmount, rate);

  // 转预收金额=收款金额+调整金额-本次核销金额
  const advanceAmount = useMemo(() => {
    if (isCrossCurrency) {
      return 0;
    }
    return NP.minus(
      NP.plus(watchedTotalReceivedAmountValue, watchedAdjustAmountValue),
      currTotalReceivedAmount,
    );
  }, [
    watchedTotalReceivedAmountValue,
    watchedAdjustAmountValue,
    currTotalReceivedAmount,
    isCrossCurrency,
  ]);

  const localAdvanceAmount = localCurrencyTransfer(advanceAmount, rate);

  // 本位币汇率损益=收款金额（按收款汇率折合本位币）+调整金额（按收款汇率折合本位币）-本次核销金额（按应收汇率折合本位币）-转预收金额（按收款汇率折合本位币）
  const localCurrencyLoss = useMemo(() => {
    return _.round(
      NP.minus(
        NP.plus(localTotalReceivedAmountValue, localAdjustAmountValue),
        NP.plus(localCurrTotalReceivedAmount, localAdvanceAmount),
      ),
      2,
    );
  }, [
    localTotalReceivedAmountValue,
    localAdjustAmountValue,
    localCurrTotalReceivedAmount,
    localAdvanceAmount,
  ]);

  const getData = async () => {
    queryReceivedFlowList(serialNumber).then((result) => {
      const { finReceivedRo = {}, finReceivedFlowRoList = [] } = result || {};

      form.setFieldsValue({
        ...finReceivedRo,
        adjustType: String(finReceivedRo?.adjustType ?? ''),
        buyerType: finReceivedRo?.buyerType === 1 ? ObjectType.Customer : ObjectType.OtherCompany,
        receivePicFileList: finReceivedRo?.receivePic
          ? [
            {
              uid: '-1',
              name: 'image.png',
              status: 'done',
              url: finReceivedRo?.receivePic,
            },
          ]
          : [],
      });

      queryReceivableList({
        buyerId: finReceivedRo?.buyerId,
        receivableFlag: 1,
      }).then((result = []) => {
        const filterData = result
          .filter((item) => item.remainReceivableAmountYuan != 0)
          .sort((a, b) => new Date(a.billDate).getTime() - new Date(b.billDate).getTime())
          .map((item) => {
            const flowItem = finReceivedFlowRoList.find(
              (flowItem) => flowItem.orderNo === item.orderNo,
            );
            console.log(finReceivedFlowRoList, flowItem);
            return {
              ...item,
              currReceivedAmount: flowItem?.receivedAmountYuan,
            };
          });

        setReceivableList(filterData);
        setEditableRowKeys(result?.map((item) => item.orderNo));
        setSelectedRowKeys(finReceivedFlowRoList?.map((item) => item.orderNo));
      });
    });
  };

  useEffect(() => {
    form.resetFields();
    setReceivableList([]);
    setEditableRowKeys([]);
    setSelectedRowKeys([]);
    if (serialNumber) {
      getData();
    }
  }, []);

  useEffect(() => {
    if (serialNumber) {
      getData();
    } else {
      form.resetFields();
      setReceivableList([]);
      setEditableRowKeys([]);
      setSelectedRowKeys([]);
    }
  }, [serialNumber]);

  useEffect(() => {
    if (cstId && cstName) {
      form.setFieldsValue({
        buyerId: cstId,
        buyerName: cstName,
        buyerType: ObjectType.Customer,
      });
    } else {
      form.resetFields();
    }
  }, [cstId, cstName]);

  console.log('accountOptions', accountOptions)

  const autoAssignOrders = () => {
    let totalReceivedAmount: number = form.getFieldValue('totalReceivedAmountYuan') || 0;
    let adjustAmount: number = form.getFieldValue('adjustAmount') || 0;
    let amountToAssign = NP.plus(totalReceivedAmount, adjustAmount);

    if (new Set(receivableList.map((item) => item.currency)).size > 1) {
      message.error(
        intl.formatMessage({ id: 'finance.supplierPayment.multiCurrencynotSupportError' }),
      );
      return;
    }
    if (currency !== receivableList?.[0]?.currency) {
      message.error(
        intl.formatMessage({ id: 'finance.supplierPayment.multiCurrencynotSupportError' }),
      );
      return;
    }

    if (amountToAssign <= 0) {
      message.error(intl.formatMessage({ id: 'finance.receive.negativeAmountError' }));
      return;
    }

    const newSelectedKeys: React.Key[] = [];
    const displayedIds = new Set(receivableList.map((item) => item.orderNo));

    const newMasterList = receivableList.map((item) => {
      const newItem = { ...item };
      // Only apply to items currently displayed
      if (displayedIds.has(newItem.orderNo)) {
        if (amountToAssign > 0) {
          const unreceived = newItem.remainReceivableAmountYuan || 0;
          if (unreceived > 0) {
            if (amountToAssign >= unreceived) {
              newItem.currReceivedAmount = unreceived;
              amountToAssign = _.round(NP.minus(amountToAssign, unreceived), 2);
              newSelectedKeys.push(newItem.orderNo);
            } else {
              newItem.currReceivedAmount = amountToAssign;
              amountToAssign = 0;
              newSelectedKeys.push(newItem.orderNo);
            }
          } else {
            newItem.currReceivedAmount = null;
          }
        } else {
          newItem.currReceivedAmount = null;
        }
      }
      return newItem;
    });

    setReceivableList(newMasterList);
    setSelectedRowKeys(newSelectedKeys);
  };

  const handleQueryReceivableList = async (params: any) => {
    const data = await queryReceivableList(params);
    const filteredData = data
      ?.filter((item) => item.remainReceivableAmountYuan != 0)
      .sort((a, b) => new Date(a.billDate).getTime() - new Date(b.billDate).getTime());
    setReceivableList(filteredData);
    setEditableRowKeys(data?.map((item) => item.orderNo));
  };

  useUpdateEffect(() => {
    setSelectedRowKeys([]);
    if (buyerId) {
      if (!isEdit) {
        handleQueryReceivableList({ buyerId, receivableFlag: 1 });
      }
      queryTotalReceivable({ buyerId }).then((result) => {
        setTotalReceivable(result ?? []);
      });
    } else {
      setTotalReceivable([]);
      setReceivableList([]);
    }
  }, [buyerId]);

  const queryAccounts = useCallback(async () => {
    let advanceAccount: AdvanceAccount;
    let memberAccountList: MemberAccountEntity[] = [];

    if (currency) {
      const memberAccountPage = await queryMemberAccountPage({ pageSize: 1000, currency });
      memberAccountList = memberAccountPage?.data?.map((item) => {
        return {
          ...item,
          label: item.memberAccountName,
          value: item.id,
        };
      });
    }

    if (buyerId && currency) {
      advanceAccount = await queryAdvanceAccount({ currency, customerId: buyerId });
      setAdvanceAccount(advanceAccount);
    }

    const accountOptions = (() => {
      if (advanceAccount) {
        return [...memberAccountList, {
          label: advanceAccount.memberAccountName,
          value: advanceAccount.id,
        }];
      }
      return memberAccountList;
    })()
    setAccountOptions(accountOptions);
    if (!serialNumber) {
      form.setFieldsValue({
        receivedAccountId: accountOptions?.[0]?.value,
        receivedAccountName: accountOptions?.[0]?.label,
      });
    }
  }, [currency, buyerId, serialNumber]);

  useEffect(() => {
    queryAccounts();
  }, [currency, buyerId, companyType]);

  useEffect(() => {
    if (buyerType === ObjectType.OtherCompany) {
      form.setFieldsValue({
        saleCompanyId: undefined,
        saleCompanyName: undefined,
      });
    }
  }, [buyerType]);

  useEffect(() => {
    if (companyType === ObjectType.OtherCompany) {

    }
  }, [buyerType]);

  const handleUpdate = (record: FinReceivableEntity) => {
    // Find the currency of the first selected item, if any
    let firstSelectedCurrency: string | undefined;
    if (selectedRowKeys.length > 0) {
      const firstSelectedItem = selectedReceivableList.find(
        (item) => item.orderNo === selectedRowKeys[0],
      );
      if (firstSelectedItem) {
        firstSelectedCurrency = firstSelectedItem.currency;
      }
    }

    // If trying to add a new item by entering an amount
    if (record.currReceivedAmount) {
      // and its currency is different from already selected items
      if (firstSelectedCurrency && record.currency !== firstSelectedCurrency) {
        message.error(
          intl.formatMessage({
            id: 'finance.receive.multiCurrencyError',
            defaultMessage: '每次收款只能核销一个币种的订单，不同币种订单请分开收款。',
          }),
        );

        // Revert the change in the list
        const revertedList = _receivableList.map((item) =>
          item.orderNo === record.orderNo ? { ...item, currReceivedAmount: null } : item,
        );
        setReceivableList(revertedList);
        return; // Stop further processing
      }
    }

    const newReceivableList = _receivableList.map((item) =>
      item.orderNo === record.orderNo
        ? { ...item, currReceivedAmount: record.currReceivedAmount }
        : item,
    );
    setReceivableList(newReceivableList);

    if (record.currReceivedAmount) {
      setSelectedRowKeys((keys) => [...new Set([...keys, record.orderNo])]);
    } else {
      setSelectedRowKeys((keys) => keys.filter((key) => key !== record.orderNo));
    }
  };

  const saveFn = async () => {
    const formData = form.getFieldsValue();
    console.log(formData);
    const selectedOrderDetailList = receivableList
      .filter((item) => selectedRowKeys.includes(item.orderNo))
      .map((item) => ({
        receivableId: item.id,
        receivedAmountYuan: item.currReceivedAmount,
        ledgerType: item.ledgerType,
      }));
    if (!selectedOrderDetailList || selectedOrderDetailList.length == 0) {
      message.warning(intl.formatMessage({ id: 'finance.receive.noOrderSelectedWarning' }));
      return false;
    }

    return saveReceived({
      ...formData,
      adjustAmount,
      buyerType: formData.buyerType === ObjectType.Customer ? 1 : 2,
      ledgerType: 1,
      finReceivedOrderDetailCmdList: selectedOrderDetailList,
      lossAmountYuan: localCurrencyLoss,
      writeOffAmount: `${currencySymbol}${currTotalReceivedAmount.toFixed(2)}`,
      advanceAmountYuan: advanceAmount,
      serialNumber,
    });
  };

  const onSave = async () => {
    const result = await saveFn();
    if (result) {
      message.success(intl.formatMessage({ id: 'common.message.submitSuccess' }));
      history.push('/finance/receive');
    }
  };

  const onSubmit = async () => {
    const serialNumber = await saveFn();
    if (serialNumber) {
      const updateReult = await updateReceivedStatus({
        serialNumber,
        status: ReceivedStatus.PENDING,
      });
      if (updateReult) {
        message.success(intl.formatMessage({ id: 'common.message.operationSuccess' }));
        history.push('/finance/receive');
      }
    }
  };

  return (
    <PageContainer>
      <ProForm submitter={false} form={form} layout="vertical" onFinish={onSave}>
        <ProCard className="rounded-lg">
          <ProForm.Group>
            <div className='relative'>
              <ProFormObject
                form={form}
                label={intl.formatMessage({ id: 'finance.receive.columns.customer' })}
                required
                rules={[REQUIRED_RULES]}
                objects={[ObjectType.Customer, ObjectType.OtherCompany]}
                fieldsName={{
                  fieldType: 'buyerType',
                  fieldName: 'buyerName',
                  fieldId: 'buyerId',
                }}
                help=" "
                // help={
                //   Boolean(totalReceivable.length) &&
                //   `${intl.formatMessage({ id: 'finance.receive.totalReceivable' })}: ${totalReceivable
                //     .map((item) => `${item.currencySymbol}${item.amount.toFixed(2)}`)
                //     .join(',')}`
                // }
                disabled={serialNumber ? true : false}
              />
              <div className='absolute left-0 bottom-[-5px] text-gray-400'>
                {
                  Boolean(totalReceivable.length) &&
                  `${intl.formatMessage({ id: 'finance.receive.totalReceivable' })}: ${totalReceivable
                    .map((item) => `${item.currencySymbol}${item.amount.toFixed(2)}`)
                    .join(',')}`}
              </div>
            </div>

            <ProFormSelect
              width={width}
              required
              rules={[REQUIRED_RULES]}
              name="receiveStoreId"
              label={intl.formatMessage({ id: 'finance.receive.receiveStore' })}
              onChange={(value, option) => {
                form.setFieldsValue({
                  receiveStoreName: option.label,
                });
              }}
              request={async () => {
                const data = await queryStoreByAccount({});
                if (data && data.length > 0) {
                  form.setFieldsValue({
                    receiveStoreId: data[0].id,
                    receiveStoreName: data[0].name,
                  });
                }
                return data?.map(({ id, name }) => ({
                  value: id,
                  label: name,
                }));
              }}
            />
            <ProFormText hidden name="receiveStoreName" />

            {
              buyerType === ObjectType.OtherCompany && <>
                <ProFormSelect
                  width={width}
                  required
                  rules={[REQUIRED_RULES]}
                  name="saleCompanyId"
                  label="销售主体"
                  dependencies={['receiveStoreId', 'buyerId', 'buyerType']}
                  onChange={(value, option) => {
                    form.setFieldsValue({
                      saleCompanyName: option.label,
                    });
                  }}
                  request={(query) => {
                    if (!query.receiveStoreId || !query.buyerId || query.buyerType === ObjectType.OtherCompany) {
                      return [];
                    }
                    return querySaleOrgList({ storeId: query.receiveStoreId, cstId: query.buyerId }).then((result) => {
                      const list: any[] =
                        // @ts-ignore
                        result?.map((item: SaleOrgEntity) => ({
                          label: item.saleOrgName,
                          value: item.saleOrgId,
                          isDefault: item.isDefault,
                        })) ?? [];
                      // 查看是否有默认值
                      if (!orderNo) {
                        let defaultItem = list.find((item) => item.isDefault);
                        if (defaultItem) {
                          if (!defaultItem) {
                            defaultItem = list?.[0];
                          }
                          if (defaultItem) {
                            form?.setFieldsValue?.({
                              saleCompanyId: defaultItem.value,
                              saleCompanyName: defaultItem.label,
                            });
                          }
                        }
                      }
                      return list;
                    });
                  }}
                />
                <ProFormText hidden name="saleCompanyName" />
              </>
            }

            <ProFormCurrency
              label={intl.formatMessage({ id: 'finance.receive.receiveCurrency' })}
              onChange={() => {
                form.setFieldsValue({
                  receivedAccountId: '',
                  receivedAccountName: '',
                });
              }}
            />

            <ProFormMoney
              customSymbol=" " // 空格不能去
              width={width}
              required
              name="totalReceivedAmountYuan"
              label={intl.formatMessage({ id: 'finance.receive.columns.receivedAmount' })}
              rules={[REQUIRED_RULES]}
            />
            <div className='relative'>
              <ProFormSelect
                width={width}
                required
                name="receivedAccountId"
                label={intl.formatMessage({ id: 'finance.receive.columns.receivedAccount' })}
                showSearch
                rules={[REQUIRED_RULES]}
                options={accountOptions}
                help=" "
                onChange={(value, option) => {
                  form.setFieldsValue({
                    receivedAccountName: option?.label,
                  });
                }}
              // help={
              //   Boolean(advanceAccount) &&
              //   `${intl.formatMessage({ id: 'finance.receive.availableBalance' })}: ${advanceAccount?.currencySymbol}${advanceAccount?.availableAmountYuan ?? 0}`
              // }
              />
              <div className='absolute left-0 bottom-[-5px] text-gray-400'>
                {
                  Boolean(advanceAccount) &&
                  `${intl.formatMessage({ id: 'finance.receive.availableBalance' })}: ${advanceAccount?.currencySymbol}${advanceAccount?.availableAmountYuan ?? 0}`
                }
              </div>
            </div>
            <ProFormText hidden name="receivedAccountName" />
            <ProFormSelect
              width={width}
              name="adjustType"
              label={intl.formatMessage({ id: 'finance.receive.adjustAmount' })}
              valueEnum={{
                [AdjustType.NONE]: intl.formatMessage({ id: 'finance.receive.adjustType.none' }),
                [AdjustType.ROUND]: intl.formatMessage({ id: 'finance.receive.adjustType.round' }),
                [AdjustType.DISCOUNT]: intl.formatMessage({
                  id: 'finance.receive.adjustType.discount',
                }),
              }}
              extra={
                <ProFormDependency name={['adjustType']}>
                  {({ adjustType }, form) => {
                    if (adjustType == AdjustType.DISCOUNT) {
                      return (
                        <div
                          style={{
                            marginTop: 10,
                            display: 'flex',
                            justifyContent: 'space-between',
                          }}
                        >
                          <ProFormMoney
                            placeholder={intl.formatMessage({ id: 'finance.receive.adjustAmount' })}
                            customSymbol=" " // 空格不能去
                            name="adjustAmountYuan"
                            width={160}
                          />
                          <ProFormText
                            name="adjustReason"
                            width={160}
                            placeholder={intl.formatMessage({ id: 'finance.receive.adjustReason' })}
                          />
                        </div>
                      );
                    }
                  }}
                </ProFormDependency>
              }
            />
            <ProForm.Item label=" " colon={false}>
              <span
                onClick={autoAssignOrders}
                className="cursor-pointer text-[#0099D3] flex items-center"
              >
                <img src={require('@/assets/icons/assign.png')} className="w-5 h-5 mr-1" />
                {intl.formatMessage({ id: 'finance.receive.autoAssign' })}
              </span>
            </ProForm.Item>
          </ProForm.Group>
        </ProCard>
        <FunProTable<FinReceivableEntity, any>
          scroll={{ x: 'max-content' }}
          className="mt-4"
          rowKey="orderNo"
          title={() => (
            <Flex justify="space-between" align="flex-end">
              <LeftTitle title={intl.formatMessage({ id: 'finance.receive.writeOffOrder' })} />
              <Space>
                <Checkbox.Group value={overdueType} onChange={(e) => setOverdueType(e)}>
                  <Checkbox value={'Due'}>
                    {intl.formatMessage({ id: 'finance.receive.filter.due' })}
                  </Checkbox>
                  <Checkbox value={'Over Due'}>
                    {intl.formatMessage({ id: 'finance.receive.filter.overdue' })}
                  </Checkbox>
                </Checkbox.Group>
                <Search
                  placeholder={intl.formatMessage({
                    id: 'finance.receive.placeholders.businessOrderNo',
                  })}
                  allowClear
                  onSearch={setOrderNo}
                  style={{ width: 300 }}
                />
              </Space>
            </Flex>
          )}
          search={false}
          pagination={false}
          rowSelection={{
            selectedRowKeys,
            onChange: (keys, rows) => {
              if (rows.length > 1) {
                const firstCurrency = rows[0].currency;
                const allSameCurrency = rows.every((row) => row.currency === firstCurrency);
                if (!allSameCurrency) {
                  message.error(
                    intl.formatMessage({
                      id: '每次收款只能核销一个币种的订单，不同币种订单请分开收款。',
                    }),
                  );
                  return;
                }
              }

              const newReceivableList = _receivableList.map((item) => {
                if (keys.includes(item.orderNo)) {
                  // If newly selected, fill amount
                  if (!selectedRowKeys.includes(item.orderNo)) {
                    return { ...item, currReceivedAmount: item.remainReceivableAmountYuan };
                  }
                } else {
                  // If deselected, clear amount
                  if (selectedRowKeys.includes(item.orderNo)) {
                    return { ...item, currReceivedAmount: undefined };
                  }
                }
                return item;
              });
              setReceivableList(newReceivableList);
              setSelectedRowKeys(keys);
            },
          }}
          editable={{
            editableKeys,
          }}
          dataSource={receivableList}
          options={false}
          columns={getCreateColumns(intl, { handleUpdate })}
        />
        <ProCard>
          <ProForm.Group colProps={{ span: 24 }}>
            <ProForm.Group>
              <ProFormUploadSingleCard
                name="receivePicFileList"
                label={intl.formatMessage({ id: 'finance.receive.receiptImage' })}
                onChange={(fileList) => {
                  console.log(fileList);
                  form?.setFieldsValue({
                    receivePicFileList: fileList,
                    receivePic: fileList?.[0]?.response?.data?.[0],
                  });
                }}
              />
              <ProFormText name="receivePic" hidden />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormTextArea
                name="remark"
                label={intl.formatMessage({ id: 'finance.receive.columns.remark' })}
                width={600}
                fieldProps={{
                  count: { max: 100, show: true },
                  maxLength: 100,
                  style: { height: 102 },
                }}
              />
            </ProForm.Group>
          </ProForm.Group>
        </ProCard>

        <ProCard>
          <div className="flex justify-end gap-8 items-end" style={{ marginBottom: 40 }}>
            <div>
              <div className="flex flex-row items-center">
                <span className="text-[16px] font-semibold text-[#000000D9]">
                  {intl.formatMessage({ id: 'finance.receive.summary.totalReceived' })}：
                </span>
                <span className="text-[24px] font-medium text-primary">
                  {currencySymbol}
                  {watchedTotalReceivedAmountValue.toFixed(2)}
                </span>
              </div>
              {!isLocalCurrency && selectedReceivableList.length > 0 && (
                <div className="flex flex-row items-center text-gray-600">
                  <span>
                    {intl.formatMessage({ id: 'finance.receive.summary.local.totalReceived' })}：
                  </span>
                  <span>${_.round(localTotalReceivedAmountValue, 2)}</span>
                </div>
              )}
            </div>
            <div>
              <div className="flex flex-row items-center">
                <span className="text-[16px] font-semibold text-[#000000D9]">
                  {intl.formatMessage({ id: 'finance.receive.summary.totalAdjust' })}：
                </span>
                <span className="text-[24px] font-medium text-primary">
                  {currencySymbol}
                  {adjustAmount.toFixed(2)}
                </span>
              </div>
              {!isLocalCurrency && selectedReceivableList.length > 0 && (
                <div className="flex flex-row items-center text-gray-600">
                  <span>
                    {intl.formatMessage({ id: 'finance.receive.summary.local.totalAdjust' })}：
                  </span>
                  <span>${_.round(localAdjustAmountValue, 2)}</span>
                </div>
              )}
            </div>
            <div>
              <div className="flex flex-row items-center">
                <span className="text-[16px] font-semibold text-[#000000D9]">
                  {intl.formatMessage({ id: 'finance.receive.summary.totalWriteOff' })}：
                </span>
                <span className="text-[24px] font-medium text-primary">
                  {selectedReceivableList[0]?.currencySymbol}
                  {currTotalReceivedAmount.toFixed(2)}
                </span>
              </div>
              {!isLocalCurrency && selectedReceivableList.length > 0 && (
                <div className="flex flex-row items-center text-gray-600">
                  <span>
                    {intl.formatMessage({ id: 'finance.receive.summary.local.totalWriteOff' })}：
                  </span>
                  <span>${_.round(localCurrTotalReceivedAmount, 2)}</span>
                </div>
              )}
            </div>
            <div>
              <div className="flex flex-row items-center">
                <span className="text-[16px] font-semibold text-[#000000D9]">
                  {intl.formatMessage({ id: 'finance.receive.summary.totalAdvance' })}：
                </span>
                <span className="text-[24px] font-medium text-primary">
                  {currencySymbol}
                  {advanceAmount.toFixed(2)}
                </span>
              </div>
              {!isLocalCurrency && selectedReceivableList.length > 0 && (
                <div className="flex flex-row items-center text-gray-600">
                  <span>
                    {intl.formatMessage({ id: 'finance.receive.summary.local.totalAdvance' })}：
                  </span>
                  <span>${_.round(localAdvanceAmount, 2)}</span>
                </div>
              )}
            </div>
            <div>
              <div className="flex flex-row items-center"></div>
              {!isLocalCurrency && selectedReceivableList.length > 0 && (
                <div className="flex flex-row items-center text-gray-600">
                  <span>{intl.formatMessage({ id: 'finance.receive.summary.local.loss' })}：</span>
                  <span
                    className={
                      localCurrencyLoss === 0
                        ? 'text-gray-600'
                        : localCurrencyLoss > 0
                          ? 'text-[#F83431]'
                          : 'text-[#00C853]'
                    }
                  >
                    {localCurrencyLoss === 0 ? '' : localCurrencyLoss > 0 ? '+' : '-'}$
                    {Math.abs(localCurrencyLoss).toFixed(2)}
                  </span>
                </div>
              )}
            </div>
          </div>
          <Flex className="flex justify-end items-center w-full">
            <Space>
              {/* <Button key="rest" danger onClick={() => back()}>
                {intl.formatMessage({ id: 'common.button.cancel' })}
              </Button> */}
              <Button type="primary" ghost onClick={onSave}>
                {intl.formatMessage({ id: 'common.button.save' })}
              </Button>
              <Button type="primary" key="launch" onClick={onSubmit}>
                {intl.formatMessage({ id: 'finance.receive.confirm' })}
              </Button>
            </Space>
          </Flex>
        </ProCard>
      </ProForm>
    </PageContainer>
  );
};

export default withKeepAlive(AddReceived);
