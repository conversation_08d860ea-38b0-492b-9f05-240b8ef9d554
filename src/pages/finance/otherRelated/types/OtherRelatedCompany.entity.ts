export enum Status {
  ENABLE = 1,
  DISABLE = 0,
}

export enum IsDefault {
  YES = '0', // 默认
  NO = '1', // 非默认
}

export interface OtherRelatedCompanyEntity {
  /**
   * 财务其他往来单位地址信息
   */
  otherRelatedCompanyAddressList?: OtherRelatedCompanyAddressList[];
  /**
   * 财务其他往来单位联系人信息
   */
  otherRelatedCompanyConcatList?: OtherRelatedCompanyConcatList[];
  /**
   * 财务其他往来单位基础信息
   */
  otherRelatedCompanyInfo?: OtherRelatedCompanyInfo;
}

export interface OtherRelatedCompanyAddressList {
  postCode?: string;
  /**
   * 区
   */
  area?: string;
  /**
   * 区code
   */
  areaCode?: string;
  /**
   * 省市区code列表，兼容前端展示方式
   */
  areaCodeList?: string[];
  /**
   * 是否可编辑0-否1-是
   */
  canEdit?: string;
  /**
   * 市
   */
  city?: string;
  /**
   * 市code
   */
  cityCode?: string;
  /**
   * 财务其他往来单位id
   */
  companyId?: string;
  /**
   * 联系人-姓
   */
  concatFirstName?: string;
  /**
   * 联系人-名
   */
  concatLastName?: string;
  /**
   * 联系方式
   */
  concatPhone?: string;
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人名称
   */
  creator?: string;
  /**
   * 详细地址
   */
  detailAddress?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 是否默认;0-默认地址1-非默认地址
   */
  isDefault?: IsDefault;
  /**
   * 删除标记;0正常1已删除
   */
  isDelete?: string;
  /**
   * 省
   */
  province?: string;
  /**
   * 省code
   */
  provinceCode?: string;
  /**
   * 更新人
   */
  updatePerson?: string;
  /**
   * 更新人名称
   */
  updater?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
}

export interface OtherRelatedCompanyConcatList {
  /**
   * 是否可编辑0-否1-是
   */
  canEdit?: string;
  /**
   * 财务其他往来单位id
   */
  companyId?: string;
  /**
   * 联系人-姓
   */
  concatFirstName?: string;
  /**
   * 联系人-名
   */
  concatLastName?: string;
  /**
   * 联系方式
   */
  concatPhone?: string;
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人名称
   */
  creator?: string;
  /**
   * 邮箱
   */
  email?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 是否默认联系人
   */
  isDefault?: IsDefault;
  /**
   * 删除标记;0正常1已删除
   */
  isDelete?: string;
  /**
   * 职务
   */
  post?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 性别;0-男1-女
   */
  sex?: string;
  /**
   * 更新人
   */
  updatePerson?: string;
  /**
   * 更新人名称
   */
  updater?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
}

/**
 * 财务其他往来单位基础信息
 */
export interface OtherRelatedCompanyInfo {
  /**
   * ABN
   */
  abn?: string;
  /**
   * 财务其他往来单位code
   */
  companyCode?: string;
  /**
   * 财务其他往来单位name
   */
  companyName?: string;
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人名称
   */
  creator?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 删除标记;0正常1已删除
   */
  isDelete?: string;
  /**
   * 门店系统侧零售商ID
   */
  memberId?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 财务其他往来单位简称
   */
  shortName?: string;
  /**
   * 财务其他往来单位状态;0-禁用1-启用
   */
  status?: string;
  /**
   * 更新人
   */
  updatePerson?: string;
  /**
   * 更新人名称
   */
  updater?: string;
  /**
   * 更新时间
   */
  updateTime?: string;

  isMultiCurrency?: 0 | 1;
}

/**
 * 结果数据
 */
export interface CompanyListItem {
  /**
   * 财务其他往来单位id
   */
  companyId?: string;
  /**
   * 财务其他往来单位name
   */
  companyName?: string;
}
