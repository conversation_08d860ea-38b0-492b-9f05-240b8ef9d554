import SubTitle from '@/components/common/SubTitle';
import type { CommonModelForm } from '@/types/CommonModelForm';
import type { EditableFormInstance } from '@ant-design/pro-components';
import { DrawerForm, EditableProTable, ProCard, ProFormText } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { ConfigProvider, message } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { add } from 'lodash';
import { useRef, useState } from 'react';
import { InputPostListTableColumns } from '../../../detail/config/inputPostListTableColumns';
import type {
  InPutDetailPostEntity,
  StockInDetailRoList,
} from '../../../detail/types/input.detail.post.entity';
import { queryInPutDetailPost } from '../../../services';

export default (props: CommonModelForm<string, StockInDetailRoList>) => {
  const intl = useIntl();
  const [recordData, setRecordData] = useState<InPutDetailPostEntity>({});
  // 可编辑行
  const [editorRows, setEditorRows] = useState<string[]>([]);
  const [form] = useForm();
  const formRef = useRef<EditableFormInstance>();
  useAsyncEffect(async () => {
    if (props.recordId) {
      const data = await queryInPutDetailPost({ stockInId: props.recordId });
      if (data) {
        setRecordData(data);
        form.setFieldsValue(data?.stockInRo);
        form.setFieldsValue(data);
        if (data?.stockInDetailRoList) {
          setEditorRows(data?.stockInDetailRoList.map((s) => s.id ?? ''));
        }
      }
    }
  }, [props.visible]);

  const onFinish = async () => {
    form
      ?.validateFields()
      .then((values) => {
        const sum = values?.stockInDetailRoList
          ?.map((s: StockInDetailRoList) => s.remainAmount as number)
          ?.reduce(
            (accumulator: number, currentValue: number) =>
              add(Number(accumulator), Number(currentValue)),
            0,
          );
        if (sum === 0) {
          message.error(intl.formatMessage({ id: 'stocks.input.modal.message.atLeastOneProduct' }));
          return;
        }
        return props.onOk!({ ...values, stockInDetailCmdList: values.stockInDetailRoList });
      })
      .catch((info) => {
        message.error(info.errorFields[0]?.errors[0]);
        return false;
      });
  };

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#F49C1F',
        },
        components: {
          InputNumber: {
            controlWidth: 160,
          },
        },
      }}
    >
      <DrawerForm
        title={props.title}
        width={1080}
        layout="horizontal"
        form={form}
        drawerProps={{
          classNames: {
            body: 'bg-[#f2f2f2]',
          },
          destroyOnClose: true,
          onClose: props.onCancel,
          maskClosable: false,
        }}
        submitter={{
          searchConfig: {
            submitText: intl.formatMessage({ id: 'stocks.input.modal.button.confirmInput' }),
          },
        }}
        open={props.visible}
        validateTrigger={'onchang'}
        onFinish={onFinish}
      >
        <ProFormText name="id" hidden />
        <ProFormText name="origBillNo" hidden />
        <ProCard className="mb-4" bodyStyle={{ paddingTop: 0, paddingBottom: 16 }}>
          <EditableProTable
            headerTitle={<SubTitle text={intl.formatMessage({ id: 'stocks.input.modal.subtitle.goodsDetail' })} />}
            columns={InputPostListTableColumns({ isDetail: true, isEdit: true })}
            rowKey="id"
            search={false}
            options={false}
            name="stockInDetailRoList"
            recordCreatorProps={false}
            scroll={{ x: 1000 }}
            editable={{
              type: 'multiple',
              editableKeys: editorRows,
              actionRender: (row, config, defaultDom) => [],
            }}
          />
        </ProCard>
      </DrawerForm>
    </ConfigProvider>
  );
};
