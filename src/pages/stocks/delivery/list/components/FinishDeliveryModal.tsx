import { compressImage } from '@/utils/fileUtils';
import { ModalForm, ProFormUploadButton } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Flex, message } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { finishDelivery } from '../../services';

interface FinishDeliveryModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  deliveryId: string;
}

const FinishDeliveryModal: React.FC<FinishDeliveryModalProps> = ({
  visible,
  onClose,
  onSuccess,
  deliveryId,
}) => {
  const intl = useIntl();
  const t = (id: string, ...rest: any[]) => intl.formatMessage({ id }, ...rest);
  const [form] = useForm();

  const handleFinish = async (values) => {
    const { files = [] } = values;
    try {
      const result = await finishDelivery({
        id: deliveryId,
        images: files.map((info) => info?.response?.data?.[0]).join(','),
      });
      if (!result) return false;
      message.success(t('common.message.operation.success'));
      onSuccess();
      onClose();
    } finally {
    }
  };

  return (
    <ModalForm
      title={t('stocks.delivery.finishModal.title')}
      open={visible}
      form={form}
      modalProps={{ destroyOnClose: true }}
      width={500}
      onFinish={handleFinish}
      onOpenChange={(visible) => {
        if (!visible) {
          onClose();
        }
      }}
      submitter={{
        render: (_, dom) => (
          <Flex justify="center" className="w-full" gap={20}>
            {dom}
          </Flex>
        ),
      }}
    >
      <div className="py-4">
        <div className="mb-4 p-3 bg-gray-50 rounded border border-gray-200">
          <ProFormUploadButton
            name="files"
            listType="picture-card"
            action="/apigateway/public/upload/object/batch"
            accept=".jpg,.png,.jpeg,.gif,.webp"
            className="w-full"
            fieldProps={{
              maxCount: 2,
              multiple: true,
              beforeUpload: (file) => compressImage(file),
            }}
            help={'Delivery Photo（Optional). Upload a maximum of 2 images'}
          />
        </div>
      </div>
    </ModalForm>
  );
};

export default FinishDeliveryModal;
