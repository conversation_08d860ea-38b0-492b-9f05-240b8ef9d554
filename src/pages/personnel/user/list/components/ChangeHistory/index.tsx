import LeftTitle from '@/components/LeftTitle';
import { ProCard } from '@ant-design/pro-components';
import { pageQueryChangeRecord } from '@/pages/personnel/user/services';
import { UserChangeRecord } from '@/pages/personnel/user/list/types/user.change.record';
import { AccountWorkingScheduleCmdList } from '@/pages/personnel/user/list/types/user.entity';
import { WorkdayEnum, WorkdayEnumName } from '@/pages/personnel/user/list/types/workday.enum';
import React, { useEffect, useState } from 'react';
import { Drawer, DrawerProps, Empty } from 'antd';
import { TimeFormat } from '@/components/common/TimeFormat';
import { fillAccountWorkingSchedule } from '@/pages/personnel/user/list/components/modal';

export interface ChangeHistoryProps extends DrawerProps {
  accountId?: string;
}

export default function ChangeHistory(props: ChangeHistoryProps) {
  const { accountId, ...rest } = props;
  const [list, setList] = useState<UserChangeRecord[]>();

  useEffect(() => {
    if (props.open && props.accountId) {
      pageQueryChangeRecord({ accountId, pageSize: 9999, pageNo: 1 }).then((res) => {
        setList(
          // @ts-ignore
          res.data?.map((item: UserChangeRecord) => ({
            ...item,
            newData: JSON.parse(item.newData!),
          })),
        );
      });
    }
    return () => {
      setList(undefined);
    };
  }, [props.open, props.accountId]);

  const handleViewData = (data: AccountWorkingScheduleCmdList[]) => {
    const fullData = fillAccountWorkingSchedule(data);

    return (
      <table className="w-full table-fixed">
        <tr>
          {fullData.map((item) => (
            <th className="bg-gray-100 py-3">
              {/** @ts-ignore **/}
              {[WorkdayEnumName[WorkdayEnum[item.scheduledWorkday]]]}
            </th>
          ))}
        </tr>
        <tr>
          {fullData.map((m) => (
            <td className="align-top">
              <div className="flex flex-col gap-2 mt-2 mx-3">
                {m.scheduledHours?.map((n) => (
                  <div className="flex justify-center gap-3 border py-2 border-solid border-[#ddd]">
                    <div>{n.startTime}</div>
                    <div>~</div>
                    <div>{n.endTime}</div>
                  </div>
                ))}
              </div>
            </td>
          ))}
        </tr>
      </table>
    );
  };

  return (
    <Drawer
      {...rest}
      classNames={{
        body: 'bg-gray-100 !p-0',
      }}
      title="修改记录"
      width={1200}
    >
      <div className="p-4 flex flex-col gap-5">
        {list?.length === 0 && <Empty className="mt-60" />}
        {list?.map(
          ({
            // @ts-ignore
            newData: { hireDate, categoryDesc, hireStateDesc, accountWorkingScheduleList },
            createTime,
          }) => (
            <ProCard
              className="rounded-2xl"
              title={
                <LeftTitle
                  title={
                    <span>
                      修改时间: <TimeFormat time={createTime!} showTime={true} />
                    </span>
                  }
                />
              }
            >
              <div className="flex gap-10">
                {hireDate && (
                  <div className="mb-5">
                    入职时间: <TimeFormat time={hireDate} />
                  </div>
                )}
                {categoryDesc && <div className="mb-5">雇佣类型: {categoryDesc}</div>}
                {hireStateDesc && <div className="mb-5">雇佣状态: {hireStateDesc}</div>}
              </div>
              {accountWorkingScheduleList && (
                <div>
                  <div className="font-medium mb-3">工作时间</div>
                  {handleViewData(accountWorkingScheduleList)}
                </div>
              )}
            </ProCard>
          ),
        )}
      </div>
    </Drawer>
  );
}
