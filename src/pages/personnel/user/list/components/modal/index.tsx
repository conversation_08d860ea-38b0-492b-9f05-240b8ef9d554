import { queryRolListPost } from '@/pages/system/role/services';
import { allStoreSimpleQuery, queryPostDetail } from '@/pages/personnel/user/services';
import { CommonModelForm } from '@/types/CommonModelForm';
import {
  DrawerForm,
  ProFormCascader,
  ProFormGroup,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-form';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { useForm } from 'antd/lib/form/Form';
import { PostEntity } from '../../types/post.entity';
import {
  ProCard,
  ProForm,
  ProFormDatePicker,
  ProFormList,
  ProFormRadio,
} from '@ant-design/pro-components';
import LeftTitle from '@/components/LeftTitle';
import { genderOptions } from '@/pages/personnel/user/list/config/gender.type';
import { HireType, hireTypeOptions } from '@/pages/personnel/user/list/config/hire.type';
import { HireStatus, hireStatusOptions } from '@/pages/personnel/user/list/config/hire.status';
import FileUploadList from '@/pages/personnel/user/list/components/FileUploadList';
import dayjs from 'dayjs';
import { workdayEnumOptions } from '@/pages/personnel/user/list/types/workday.enum';
import WorkTimeCell from '@/pages/personnel/user/list/components/WorkTimeCell';
import { queryDistrictAreaTree } from '@/pages/system/store/services';
import { useEffect, useState } from 'react';
import { debounce } from 'lodash';
import { queryPositionList } from '@/pages/personnel/property/services';
import { PositionState } from '@/pages/personnel/property/types/position.state';

export default (props: CommonModelForm<string | undefined, PostEntity>) => {
  const intl = useIntl();
  const [form] = useForm();
  const [options, setOptions] = useState<any[]>([]);

  useAsyncEffect(async () => {
    if (props.recordId && props.visible) {
      queryPostDetail(props.recordId).then((data) => {
        // @ts-ignore
        data.accountEmployment ??= {};
        // @ts-ignore
        if (!data.accountEmployment.accountWorkingScheduleList?.length) {
          // @ts-ignore
          data.accountEmployment.accountWorkingScheduleList = workdayEnumOptions.map((option) => ({
            scheduledWorkday: option.value,
            scheduledHours: option.isDefault ? [{ startTime: '08:30', endTime: '17:30' }] : [],
          }));
        }
        // @ts-ignore
        data.accountEmployment?.images?.forEach((item: any) => {
          item.id = new Date().getTime() + Math.random();
        });
        // @ts-ignore
        data.accountSensitive?.images?.forEach((item: any) => {
          item.id = new Date().getTime() + Math.random();
        });
        // 格式化省市区
        // @ts-ignore
        const { addressList = [] } = data.account || {};
        const address = addressList[0] || {};
        if (address.provinceCode && address.prefectureCode) {
          // @ts-ignore
          data.account.addressList[0].areaCode = [address.provinceCode, address.prefectureCode];
        }
        form.setFieldsValue(data);
      });
    } else {
      form.resetFields();
    }
  }, [props.visible]);

  const postCode = ProForm.useWatch(['account', 'addressList', 0, 'postCode'], form ?? {});

  useEffect(() => {
    if (postCode) {
      handlePostCodeChange(postCode);
    } else {
      setOptions([]);
    }
  }, [postCode]);

  const handlePostCodeChange = debounce((value: string) => {
    queryDistrictAreaTree({ postCode: value }).then((res) => {
      // @ts-ignore
      setOptions(res);
    });
  }, 300);

  return (
    <DrawerForm
      title={props.title}
      open={props.visible}
      readonly={props.readOnly}
      width="1200px"
      drawerProps={{
        classNames: {
          body: 'bg-[#f2f2f2] !pt-[12px]',
        },
        onClose: props.onCancel,
        maskClosable: false,
      }}
      // @ts-ignore
      submitter={!props.readOnly}
      form={form}
      onFinish={props.onOk}
      loading={props.loading}
      grid={true}
      layout={props.readOnly ? 'horizontal' : 'vertical'}
      rowProps={{ gutter: [0, 14] }}
    >
      <ProFormText name="id" hidden={true} />
      <ProCard className="rounded-lg" title={<LeftTitle title="个人信息" />}>
        <ProFormGroup>
          <ProFormText name={['account', 'id']} hidden={true} />
          <ProFormText
            name={['account', 'firstName']}
            label="First Name"
            colProps={{ span: 8 }}
            rules={[
              {
                required: !props.readOnly,
              },
            ]}
          />
          <ProFormText
            name={['account', 'lastName']}
            label="Last Name"
            colProps={{ span: 8 }}
            rules={[
              {
                required: !props.readOnly,
              },
            ]}
          />
          <ProFormRadio.Group
            options={genderOptions}
            name={['account', 'gender']}
            label="性别"
            colProps={{ span: 8 }}
          />
          <ProFormText name={['account', 'phone']} label="手机号" colProps={{ span: 8 }} />
          <ProFormText
            name={['account', 'email']}
            label="邮箱"
            colProps={{ span: 8 }}
            rules={[
              {
                required: !props.readOnly,
              },
            ]}
          />
          <ProFormDatePicker
            name={['account', 'birthDate']}
            label="出生日期"
            colProps={{ span: 8 }}
            fieldProps={{ className: 'w-full' }}
            transform={(value) => (value ? dayjs(value).format('YYYY-MM-DD 00:00:00') : undefined)}
          />
          <ProFormText
            name={['account', 'addressList', 0, 'postCode']}
            label="邮编"
            colProps={{ span: 8 }}
          />
          <ProFormCascader
            fieldProps={{ options, fieldNames: { label: 'areaName', value: 'areaId' } }}
            name={['account', 'addressList', 0, 'areaCode']}
            label="州/区"
            colProps={{ span: 8 }}
          />
          <ProFormText
            name={['account', 'addressList', 0, 'address']}
            label="详细地址"
            colProps={{ span: 8 }}
          />
        </ProFormGroup>
      </ProCard>
      <ProCard className="rounded-lg" title={<LeftTitle title="雇佣信息" />}>
        <ProFormGroup>
          <ProFormDatePicker
            name={['accountEmployment', 'hireDate']}
            label="入职日期"
            colProps={{ span: 8 }}
            fieldProps={{ className: 'w-full' }}
            transform={(value) => (value ? dayjs(value).format('YYYY-MM-DD 00:00:00') : undefined)}
            rules={[
              {
                required: !props.readOnly,
              },
            ]}
          />
          <ProFormSelect
            name={['accountEmployment', 'category']}
            label="雇佣类型"
            initialValue={HireType.Full_Time}
            options={hireTypeOptions}
            colProps={{ span: 8 }}
            rules={[
              {
                required: !props.readOnly,
              },
            ]}
          />
          <ProFormSelect
            name={['accountEmployment', 'positionIds']}
            label="职位"
            colProps={{ span: 8 }}
            request={() =>
              queryPositionList({ state: PositionState.Enabled, pageSize: 9999, pageNo: 1 }).then(
                (res) => res?.data ?? [],
              )
            }
            fieldProps={{
              mode: 'multiple',
              showSearch: true,
              maxTagCount: 3,
              fieldNames: { label: 'name', value: 'id' },
            }}
          />
          <ProFormSelect
            name={['accountEmployment', 'storeIds']}
            label="所在门店"
            colProps={{ span: 8 }}
            mode="multiple"
            fieldProps={{ fieldNames: { label: 'name', value: 'id' } }}
            // @ts-ignore
            request={allStoreSimpleQuery}
            rules={[
              {
                required: !props.readOnly,
              },
            ]}
          />
          <ProFormText
            name={['accountEmployment', 'hourlyPay']}
            label="时薪"
            colProps={{ span: 8 }}
          />
          <ProFormDatePicker
            name={['accountEmployment', 'internshipEndDate']}
            label="试用期结束时间"
            colProps={{ span: 8 }}
            fieldProps={{ className: 'w-full' }}
            transform={(value) => (value ? dayjs(value).format('YYYY-MM-DD 23:59:59') : undefined)}
          />
          <ProFormSelect
            name={['accountEmployment', 'roleIds']}
            label="角色"
            colProps={{ span: 8 }}
            mode="multiple"
            fieldProps={{
              fieldNames: { label: 'name', value: 'id' },
            }}
            // @ts-ignore
            request={queryRolListPost}
            rules={[
              {
                required: !props.readOnly,
              },
            ]}
          />
          <ProFormRadio.Group
            name={['accountEmployment', 'hireState']}
            label="雇佣状态"
            initialValue={HireStatus.Employed}
            options={hireStatusOptions}
            colProps={{ span: 8 }}
            rules={[
              {
                required: !props.readOnly,
              },
            ]}
          />
          <ProFormDatePicker
            name={['accountEmployment', 'terminationDate']}
            label="离职日期"
            colProps={{ span: 8 }}
            fieldProps={{ className: 'w-full' }}
            transform={(value) => (value ? dayjs(value).format('YYYY-MM-DD 00:00:00') : undefined)}
          />
          <ProFormText
            name={['accountEmployment', 'terminationReason']}
            label="离职原因"
            colProps={{ span: 8 }}
            fieldProps={{ className: 'w-full' }}
          />
        </ProFormGroup>
        <ProFormGroup>
          <div className="mt-3 w-full">
            <div className="font-medium mb-3">工作时间</div>
            <ProFormList
              name={['accountEmployment', 'accountWorkingScheduleList']}
              initialValue={workdayEnumOptions.map((option) => ({
                scheduledWorkday: option.value,
                scheduledHours: option.isDefault
                  ? [
                      {
                        startTime: '08:30',
                        endTime: '17:30',
                      },
                    ]
                  : [],
              }))}
              creatorButtonProps={false}
              containerClassName="flex"
              itemRender={() => (
                <div className="flex-1">
                  <div className="bg-[#f5f5f5] text-center font-medium py-2">
                    <ProFormSelect
                      name="scheduledWorkday"
                      options={workdayEnumOptions}
                      readonly={!props.readOnly}
                      formItemProps={{ className: 'mb-0' }}
                    />
                  </div>
                  <WorkTimeCell readonly={props.readOnly} />
                </div>
              )}
            />
          </div>
        </ProFormGroup>
        <div className="font-medium mb-1">雇佣文件</div>
        <FileUploadList form={form} name="accountEmployment" readonly={props.readOnly} />
      </ProCard>
      <ProCard className="rounded-lg" title={<LeftTitle title="银行信息" />}>
        <ProFormGroup>
          <ProFormText
            name={['accountBankList', 0, 'bankAccount']}
            label="银行行号"
            colProps={{ span: 8 }}
          />
          <ProFormText
            name={['accountBankList', 0, 'accountName']}
            label="账号名称"
            colProps={{ span: 8 }}
          />
          <ProFormText
            name={['accountBankList', 0, 'accountNumber']}
            label="银行账号"
            colProps={{ span: 8 }}
          />
        </ProFormGroup>
      </ProCard>
      <ProCard className="rounded-lg" title={<LeftTitle title="年金信息" />}>
        <ProFormGroup>
          <ProFormText
            name={['accountSuperannuationList', 0, 'fundName']}
            label="年金所属公司"
            colProps={{ span: 8 }}
          />
          <ProFormText
            name={['accountSuperannuationList', 0, 'usi']}
            label="年金公司产品号码"
            colProps={{ span: 8 }}
          />
          <ProFormText
            name={['accountSuperannuationList', 0, 'accountNumber']}
            label="员工年金账号"
            colProps={{ span: 8 }}
          />
        </ProFormGroup>
      </ProCard>
      <ProCard className="rounded-lg" title={<LeftTitle title="敏感信息" />}>
        <ProFormGroup>
          <ProFormText
            name={['accountSensitive', 'taxFileNumber']}
            label="税号"
            colProps={{ span: 8 }}
          />
          <ProFormText name={['accountSensitive', 'abn']} label="ABN" colProps={{ span: 8 }} />
          <ProFormText
            name={['accountSensitive', 'passportNumber']}
            label="护照号"
            colProps={{ span: 8 }}
          />
          <ProFormText
            name={['accountSensitive', 'visaNumber']}
            label="签证号"
            colProps={{ span: 8 }}
          />
        </ProFormGroup>
        <div className="font-medium mb-1">敏感文件</div>
        <FileUploadList name="accountSensitive" form={form} readonly={props.readOnly} />
      </ProCard>
      <ProCard className="rounded-lg" title={<LeftTitle title="备注信息" />}>
        <ProFormTextArea name={['account', 'remark']} />
      </ProCard>
    </DrawerForm>
  );
};
