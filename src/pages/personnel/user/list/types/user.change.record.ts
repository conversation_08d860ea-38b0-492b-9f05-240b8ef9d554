export interface UserChangeRecord {
  /**
   * 账户id
   */
  accountId?: string;
  /**
   * 工作时间信息
   */
  accountWorkingScheduleList?: AccountWorkingScheduleList[];
  /**
   * 变更原因
   */
  changeReason?: string;
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * None
   */
  isDelete?: number;
  /**
   * None
   */
  memberId?: string;
  /**
   * newData
   */
  newData?: string;
  /**
   * 原始数据
   */
  originalData?: string;
  /**
   * 变更类型：1-工作时间安排变更，2-雇佣类别变更
   */
  type?: string;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
}

export interface AccountWorkingScheduleList {
  /**
   * 账户id
   */
  accountId?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 休息时间
   */
  restHours?: RestHour[];
  /**
   * 工作时间
   */
  scheduledHours?: ScheduledHour[];
  /**
   * 周一到周日
   */
  scheduledWorkday?: number;
  /**
   * 周一到周日
   */
  scheduledWorkdayDesc?: string;
  /**
   * 总工作时长
   */
  totalHours?: string;
}

export interface RestHour {
  /**
   * endTime
   */
  endTime?: string;
  /**
   * startTime
   */
  startTime?: string;
}

export interface ScheduledHour {
  /**
   * endTime
   */
  endTime?: string;
  /**
   * startTime
   */
  startTime?: string;
}
