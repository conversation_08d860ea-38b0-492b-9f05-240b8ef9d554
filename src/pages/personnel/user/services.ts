import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';
import { type PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { type PostEntity } from './list/types/post.entity';
import type { PostSelect } from './list/types/post.select';
import { UserEntity } from './list/types/user.entity';
import { UserChangeRecord } from '@/pages/personnel/user/list/types/user.change.record';
import { PageQueryChangeRecordRequest } from '@/pages/personnel/user/list/types/page.query.change.record.request';

/**
 * 列表查询
 * @param params
 * @returns
 */
export const queryPostList = async (params: Partial<PostEntity> & PageRequestParamsType) => {
  return request<PageResponseDataType<PostEntity>>(`/ipmspassport/AccountFacade/pageQuery`, {
    data: params,
  });
};

/**
 * 新增用户
 * @param params
 */
export const insertUser = async (params: UserEntity) => {
  return request<boolean>(`/ipmspassport/AccountFacade/insert`, {
    data: params,
  });
};

/**
 * 更新用户
 * @param params
 */
export const updateUser = async (params: UserEntity) => {
  return request<boolean>(`/ipmspassport/AccountFacade/update`, {
    data: params,
  });
};

/**
 * 详情查询
 * @param id
 * @returns
 */
export const queryPostDetail = async (id: string) => {
  return request<UserEntity>(`ipmspassport/AccountFacade/queryDetailFull`, {
    data: { id },
  });
};

/**
 * 修改状态
 * @param params
 * @returns
 */
export const modifyStatusPost = async (params: Partial<PostEntity>) => {
  return request<string>(`/ipmspassport/AccountFacade/modifyStatus`, {
    data: params,
  });
};

/**
 * 查询门店
 * @returns
 */
export const allSimpleQuery = async ({ status = YesNoStatus.YES }: { status?: YesNoStatus }) => {
  return request<PostSelect[]>(`/ipmspassport/StoreFacade/listQuerySimple`, { data: { status } });
};

export const allStoreSimpleQuery = async (params: { status?: YesNoStatus }) => {
  return request<PostSelect[]>(`/ipmspassport/StoreFacade/listQuerySimple`, { data: params });
};

/**
 * 查询门店关联的账户列表
 * @returns
 */
export const queryAccountSelectByStoreId = async (params: { storeId?: string }) => {
  return request<PostSelect[]>(`/ipmspassport/AccountFacade/queryStoreAccount`, { data: params });
};
/**
 * 查询当前登录用户所有权限门店
 * @returns
 */
export const queryStoreByAccount = async (params?: {
  accountId?: string;
  status?: 0 | 1;
}): Promise<PostSelect[]> => {
  return request(`/ipmspassport/AccountFacade/queryStore`, { data: params ?? {} });
};

/**
 * 查询账户/业务员/员工列表
 * @returns
 */
export const accountListQuerySimple = async ({
  status = YesNoStatus.YES,
  name = '',
}: {
  status?: YesNoStatus;
  name?: string;
}): Promise<PostSelect[]> => {
  return request(`/ipmspassport/AccountFacade/listQuerySimple`, {
    data: { status, name },
  });
};

/**
 * 分页查询变更详情
 * @param params
 */
export const pageQueryChangeRecord = async (params: PageQueryChangeRecordRequest) => {
  return request<PageResponseDataType<UserChangeRecord>>(
    `/ipmspassport/AccountFacade/pageQueryChangeRecord`,
    {
      data: params,
    },
  );
};
