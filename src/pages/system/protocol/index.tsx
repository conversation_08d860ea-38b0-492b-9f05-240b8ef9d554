import {
  <PERSON>Container
} from '@ant-design/pro-components';
import { useAsyncEffect } from 'ahooks';
import { message, UploadFile } from 'antd';
import { useState } from 'react';
import { querySysPropertyList, setSysProperty } from './services';

const Protocol = () => {
  const type = 'USER_PRIVACY_AGREEMENT';
  const [agreementList, setAgreementList] = useState<any[]>([]);

  useAsyncEffect(async () => {
    const data = await querySysPropertyList({ type }); //隐私协议

    const agreementList = data.map(item => {
      const value = JSON.parse(item.value ?? '{}');
      console.log(value);
      const { title, url } = value;
      return {
        id: item.id,
        title,
        configKey: item.configKey,
        file: [{ url, status: 'done', name: title }],
      };
    })

    setAgreementList(agreementList);

  }, []);

  const handleSaveOrUpdate = async (values: any) => {
    console.log(values);
    const { configKey = [], file, title, id } = values;
    const data = await setSysProperty({
      id,
      configKey,
      value: JSON.stringify({
        title,
        url: file?.url
      }),
      type,
    });
    if (data) {
      message.success('更新成功');
    }
  };
  const rules = [{ required: true }];

  const handleDownload = (file: UploadFile) => {
    const { name: fileName, originFileObj, url } = file;
    if (url) {
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      // 触发下载
      a.click();
    } else if (originFileObj) {
      const dataUrl = URL.createObjectURL(originFileObj);
      const a = document.createElement('a');
      a.href = dataUrl;
      a.download = fileName;
      // 触发下载
      a.click();
      // 释放内存
      URL.revokeObjectURL(dataUrl);
    }
  };

  return (
    <PageContainer>

    </PageContainer >
  );
};

export default Protocol;
