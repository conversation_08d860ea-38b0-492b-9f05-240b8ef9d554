import { request } from '@/utils/request';
import { PrivateAgreementEntity } from './types/agreement.entity';

/**
 * 获取系统配置
 */
export const querySysPropertyList = (params) => {
  return request<PrivateAgreementEntity[]>(
    '/ipmsconsole/privateAgreement/queryPrivateAgreementList',
    {
      data: params,
    },
  );
};

/**
 * 设置系统配置
 */

export const setSysProperty = (params: PrivateAgreementEntity) => {
  return request<boolean>('/ipmsconsole/privateAgreement/savePrivateAgreement', {
    data: params,
  });
};


/**
 * 删除用户隐私协议
 */
export const deleteUserPrivacyAgreement = (params: { id: string }) => {
  return request<boolean>('/ipmsconsole/privateAgreement/deletePrivateAgreement', {
    data: params,
  });
};