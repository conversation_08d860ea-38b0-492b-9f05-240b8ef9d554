export interface PostEntity {
  /**
   * 区
   */
  districtId?: string;
  districtName?: string;
  /**
   * 市
   */
  cityId?: string;
  cityName?: string;
  /**
   * 联系人
   */
  contactPerson?: string;
  /**
   * 联系手机号
   */
  contactPhone?: string;
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 详细地址
   */
  detailAddress?: string;
  /**
   * 一体系零售商ID
   */
  etcMemberId?: string;
  /**
   * 一体系门店ID
   */
  etcStoreId?: string;
  /**
   * 一体系绑定状态：0未绑定，1已绑定
   */
  etcBindStatus?: string;
  /**
   * 门店ID
   */
  id?: string;
  /**
   * 零售商ID
   */
  memberId?: string;
  /**
   * 名称
   */
  name?: string;
  /**
   * 省
   */
  provinceId?: string;
  provinceName?: string;
  /**
   * 状态：0启用，1禁用
   */
  status?: string;
  /**
   * 类型：0主门店，1子门店
   */
  type?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 门店的唯一标识符
   */
  storeId?: string;

  /**
   * 	门店地址（省市区+详细地址）
   */
  storeAddress?: string;

  /**
   * 销售主体ID
   */
  saleEntityId?: string;

  /**
   * 销售主体名称
   */
  saleEntityName?: string;
}
