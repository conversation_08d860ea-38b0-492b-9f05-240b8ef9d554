import { QueryStoreListRequest } from '@/pages/system/store/list/types/query.store.list.request';
import { type PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { ResponseDataType } from '@/types/ResponseDataType';
import { request } from '@/utils/request';
import { PostDistrictArea } from './list/types/post.district.area';
import { type PostEntity } from './list/types/post.entity';
import { QuerySaleOrgListRequest } from '@/pages/system/store/types/query.sale.org.list.request';
import { SaleOrgEntity } from '@/pages/system/store/types/sale.org.entity';
import { QuerySaleOrgListAllRequest } from '@/pages/system/store/types/query.sale.org.list.all.request';
import { SaleOrgAllEntity } from '@/pages/system/store/types/sale.org.all.entity';

/**
 * 列表查询
 * @param params
 * @returns
 */
export const queryStoreList = async (params: QueryStoreListRequest & PageRequestParamsType) => {
  return request<PageResponseDataType<PostEntity>>(`/ipmspassport/StoreFacade/pageQuery`, {
    data: params,
  });
};

/**
 * 详情查询
 * @param params
 * @returns
 */
export const queryPostDetail = async (params: Partial<PostEntity>) => {
  return request<PostEntity>(`/ipmspassport/StoreFacade/detail`, {
    data: params,
  });
};
/**
 * 新增
 * @param params
 * @returns
 */
export const createPost = async (params: Partial<PostEntity>) => {
  return request<ResponseDataType<boolean>>(`/ipmspassport/StoreFacade/create`, {
    origin: true,
    data: params,
  });
};
/**
 * 编辑
 * @param params
 * @returns
 */
export const modifyPost = async (params: Partial<PostEntity>) => {
  return request<ResponseDataType<boolean>>(`/ipmspassport/StoreFacade/modify`, {
    origin: true,
    data: params,
  });
};

/**
 * 修改状态
 * @param params
 * @returns
 */
export const modifyStatusPost = async (params: Partial<PostEntity>) => {
  return request<string>(`/ipmspassport/StoreFacade/modifyStatus`, {
    data: params,
  });
};

/**
 * 查询区级地址树
 * @param params
 */
export const queryDistrictAreaTree = async (params: { postCode?: string }) => {
  return request<PostDistrictArea[]>(`/ipmsconsole/AreaFacade/queryDistrictAreaTree`, {
    data: params,
  });
};

/**
 * 查询销售主体列表
 * 门店管理使用
 * @param params
 */
export const querySaleOrgListAll = async (params: QuerySaleOrgListAllRequest) => {
  return request<SaleOrgAllEntity[]>(`/ipmspassport/SaleEntityFacade/queryList`, {
    data: params,
  });
};

/**
 * 查询可选销售主体列表
 * 销售开单/收款使用
 */
export const querySaleOrgList = async (params: QuerySaleOrgListRequest) => {
  return request<SaleOrgEntity[]>(`/ipmsconsole/saleOrgFacade/querySaleOrgList`, {
    data: params,
  });
};
