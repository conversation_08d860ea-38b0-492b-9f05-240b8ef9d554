export enum NoticeType {
  NOTIFY = 1,
  POPUP = 2,
  BANNER = 3,
}


export const NoticeTypeValueEnum = (intl: any) => {
  const t = (id: string) => intl.formatMessage({ id });
  return {
    [NoticeType.NOTIFY]: { text: t('system.messageMgr.noticeType.notify') },
    [NoticeType.POPUP]: { text: t('system.messageMgr.noticeType.popup') },
    [NoticeType.BANNER]: { text: t('system.messageMgr.noticeType.banner') },
  }
};

export const NoticeTypeOptions = (intl: any) => {
  const t = (id: string) => intl.formatMessage({ id });
  return [
    { label: t('system.messageMgr.noticeType.notify'), value: NoticeType.NOTIFY },
    { label: t('system.messageMgr.noticeType.popup'), value: NoticeType.POPUP },
    { label: t('system.messageMgr.noticeType.banner'), value: NoticeType.BANNER },
  ]
};