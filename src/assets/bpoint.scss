.bpoint-container * {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box
}

.bpoint-container *::before,.bpoint-container *::after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box
}

.bpoint-container {
  padding: 0 10px;
  margin: 0 auto;
  max-width: 500px;
  color: #333
}

.bpoint-row {
  margin-left: -10px;
  margin-right: -10px
}

.bpoint-col-sm-3,.bpoint-col-sm-4,.bpoint-col-sm-6,.bpoint-col-sm-12,.bpoint-col-md-3,.bpoint-col-md-4,.bpoint-col-md-6,.bpoint-col-md-12 {
  position: relative;
  min-height: 1px;
  padding-left: 10px;
  padding-right: 10px
}

.bpoint-col-sm-3,.bpoint-col-sm-4,.bpoint-col-sm-6,.bpoint-col-sm-12 {
  float: left
}

.bpoint-col-sm-12 {
  width: 100%
}

.bpoint-col-sm-6 {
  width: 50%
}

.bpoint-col-sm-4 {
  width: 33.33333333%
}

.bpoint-col-sm-3 {
  width: 25%
}

@media(min-width: 992px) {
  .bpoint-col-md-3,.bpoint-col-md-4,.bpoint-col-md-6,.bpoint-col-md-12 {
    float:left
  }

  .bpoint-col-md-12 {
    width: 100%
  }

  .bpoint-col-md-6 {
    width: 50%
  }

  .bpoint-col-md-4 {
    width: 33.33333333%
  }

  .bpoint-col-md-3 {
    width: 25%
  }
}

.bpoint-container::before,.bpoint-container::after,.bpoint-row::before,.bpoint-row::after {
  content: " ";
  display: table
}

.bpoint-container::after,.bpoint-row::after {
  clear: both
}

.bpoint-form-group {
  margin-bottom: 1rem
}

.bpoint-container label {
  display: inline-block;
  font-weight: bold;
  margin-bottom: .5rem
}

.bpoint-form-control {
  transition: none
}

.bpoint-form-control {
  display: block;
  width: 100%;
  height: calc(1.5em + .75rem + 2px);
  padding: .375rem .75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: .25rem;
  transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out
}

.bpoint-form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #f49c1f;
  outline: 0;
}

.bpoint-form-control.bpoint-is-valid {
  border-color: #28a745;
  padding-right: calc(1.5em + .75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(.375em + .1875rem) center;
  background-size: calc(.75em + .375rem) calc(.75em + .375rem)
}

.bpoint-form-control.bpoint-is-valid:focus {
  box-shadow: 0 0 0 .2rem rgba(40,167,69,.25)
}

.bpoint-form-control.bpoint-is-invalid {
  border-color: #dc3545;
  padding-right: calc(1.5em + .75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(.375em + .1875rem) center;
  background-size: calc(.75em + .375rem) calc(.75em + .375rem)
}

.bpoint-form-control.bpoint-is-invalid:focus {
  box-shadow: 0 0 0 .2rem rgba(220,53,69,.25)
}

.bpoint-feedback {
  display: none;
  color: #28a745;
  width: 100%;
  margin-top: .25rem;
  font-size: 80%
}

.bpoint-feedback-valid {
  display: block;
  color: #28a745
}

.bpoint-feedback-invalid {
  display: block;
  color: #dc3545
}

.bpoint-btn {
  color: #fff;
  background-color: #f49c1f;
  border: 1px solid #f49c1f;
  border-top-color: transparent;
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-left-color: transparent;
  display: inline-block;
  font-weight: 400;
  text-align: center;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: .375rem .75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: .25rem;
  cursor: pointer
}

.bpoint-btn:focus {
  color: #fff;
  background-color: #f49c1f;
  border-color: #f49c1f;
  outline: 0;
}

.bpoint-btn:hover:not([disabled]) {
  color: #fff;
  background-color: #f49c1f;
  border-color: #f49c1f;
  text-decoration: none
}

.bpoint-btn:disabled {
  opacity: .65
}

.bpoint-radio {
  display: inline-block;
  margin-right: 2rem;
  margin-bottom: 1rem
}

.bpoint-radio input {
  margin: 0 .4rem 0 0;
  padding: 0;
  overflow: visible
}
