import { Button, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { CreatePaymentResponse } from '@/pages/sales/order/list/types/create.payment.response';
import { paymentProcess } from '@/pages/sales/order/list/services';
import { LoadingOutlined } from '@ant-design/icons';

interface CardPaymentFormProps {
  payData: CreatePaymentResponse;
  onCancel?: () => void;
  onSuccess?: () => void;
  bizNo?: string;
}

export default function CardPaymentForm({
  payData,
  onCancel,
  onSuccess,
  bizNo,
}: CardPaymentFormProps) {
  useEffect(() => {
    if (payData) {
      // @ts-ignore
      BPOINT.txn.authkey.setupPaymentMethodForm(payData.authKey, {
        appendToElementId: 'cardForm',
        card: {
          number: {
            label: 'Card number',
          },
          expiry: {
            label: 'Expiry',
          },
          cvn: {
            label: 'Cvn',
          },
          name: {
            label: 'Name',
            hide: true,
          },
        },
        clientsideValidation: true,
        callback: function (code: string, data: any) {
          if (code === 'success') {
            paymentProcess({ authKey: payData.authKey, businessNo: bizNo }).then((res) => {
              if (res) {
                message.success('支付成功');
                onCancel?.();
                onSuccess?.();
              }
            });
          }
        },
      });
    }
  }, [payData]);

  return (
    <>
      <div id="cardForm"></div>
    </>
  );
}
