import { Button, message } from 'antd';
import { CopyToClipboard } from 'react-copy-to-clipboard';

interface PaymentLinkProps {
  url?: string;
  bizNo?: string;
  cstName?: string;
  amount?: number;
}

export default function PaymentLink(props: PaymentLinkProps) {
  const { url, amount, bizNo, cstName } = props;
  return (
    <div>
      <div className="text-center mt-8">
        <CopyToClipboard
          text={`收款金额: ${amount}\n收款方: Clayton\n收款对象: ${cstName}\n收款单号: ${bizNo}\n收款链接: ${url}`}
          onCopy={() => message.success('复制成功')}
        >
          <Button type="primary">复制收款信息</Button>
        </CopyToClipboard>
      </div>
    </div>
  );
}
