import { Modal, ModalProps } from 'antd';
import { CreatePaymentResponse } from '@/pages/sales/order/list/types/create.payment.response';
import { BPointType, BPointTypeName } from '@/pages/sales/order/detail/types/bpoint.type';
import Payment from './components/Payment';
import Request from './components/Request';
import MoneyText from '@/components/common/MoneyText';

export interface PaymentModalProps {
  payType?: BPointType;
  payData?: CreatePaymentResponse;
  modalData: ModalProps;
  onSuccess?: () => void;
  bizNo?: string;
  cstName?: string;
  amount?: number;
}

export default function PaymentModal({
  payType,
  payData,
  modalData,
  onSuccess,
  bizNo,
  cstName,
  amount,
}: PaymentModalProps) {
  return (
    <Modal
      footer={false}
      open={modalData.open}
      onCancel={modalData.onCancel}
      // @ts-ignore
      title={BPointTypeName[BPointType[payType]]}
      destroyOnClose={true}
    >
      <div className="space-y-6">
        {/* 头部 */}
        <div className="text-center my-6">
          <img src={require('./imgs/logo.png')} alt="logo" className="mx-auto" />
          <div className="text-primary text-[24px] my-3 font-semibold">
            <MoneyText text={amount} />
          </div>
        </div>

        {/* 收款信息 */}
        <div className="py-2 px-4 bg-gray-100 leading-6 flex flex-col gap-1">
          <div>收款方：Clayton</div>
          <div>收款对象：{cstName}</div>
          <div>收款单号：{bizNo}</div>
          {payType === BPointType.BPointLink && <div>支付链接: {payData?.paymentRequestUrl}</div>}
        </div>

        {/* 内容部分 */}
        {payType === BPointType.BPointPay && payData && (
          // @ts-ignore
          <Payment
            payData={payData}
            bizNo={bizNo}
            // @ts-ignore
            onCancel={modalData.onCancel}
            onSuccess={onSuccess}
          />
        )}
        {payType === BPointType.BPointLink && (
          <Request
            url={payData?.paymentRequestUrl}
            amount={amount}
            bizNo={bizNo}
            cstName={cstName}
          />
        )}
      </div>
    </Modal>
  );
}
