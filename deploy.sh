#!/bin/bash

# 1. 生成唯一镜像标签（用时间戳，也可用 git commit hash）
TAG=$(date +%Y%m%d%H%M%S)
# 或者用 git commit hash
# TAG=$(git rev-parse --short HEAD)

ACR_NAME=gripx
ACR_DOMAIN=gripx.azurecr.io
IMAGE_NAME=gripxstoreweb
NAMESPACE=dev
DEPLOYMENT=gripxstoreweb

echo "登录 Azure..."
az login

echo "登录 ACR..."
az acr login --name $ACR_NAME

echo "构建镜像..."
docker build --platform=linux/amd64 -t $IMAGE_NAME:$TAG .

echo "打标签并推送镜像到 ACR..."
docker tag $IMAGE_NAME:$TAG $ACR_DOMAIN/$IMAGE_NAME:$TAG
docker push $ACR_DOMAIN/$IMAGE_NAME:$TAG

echo "更新 Kubernetes 部署镜像..."
# kubectl set image deployment/$DEPLOYMENT $DEPLOYMENT=$ACR_DOMAIN/$IMAGE_NAME:$TAG -n $NAMESPACE
sed -i "" "s#gripx.azurecr.io/gripxstoreweb:.*#gripx.azurecr.io/gripxstoreweb:$TAG#g" aks-deployment.yaml

echo "部署到AKS"
kubectl apply -f aks-deployment.yaml

echo "发布完成，当前镜像版本：$TAG"
